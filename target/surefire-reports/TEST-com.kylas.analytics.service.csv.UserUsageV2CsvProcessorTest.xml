<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest" time="0.663" tests="15" errors="0" skipped="0" failures="4">
  <properties>
    <property name="java.specification.version" value="21"/>
    <property name="sun.jnu.encoding" value="UTF-8"/>
    <property name="java.class.path" value="/home/<USER>/repo/sd-customer-success/target/test-classes:/home/<USER>/repo/sd-customer-success/target/classes:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.2.0/spring-boot-starter-web-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.2.0/spring-boot-starter-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.2.0/spring-boot-starter-logging-3.2.0.jar:/home/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.4.11/logback-classic-1.4.11.jar:/home/<USER>/.m2/repository/ch/qos/logback/logback-core/1.4.11/logback-core-1.4.11.jar:/home/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.21.1/log4j-to-slf4j-2.21.1.jar:/home/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.21.1/log4j-api-2.21.1.jar:/home/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.9/jul-to-slf4j-2.0.9.jar:/home/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/home/<USER>/.m2/repository/org/yaml/snakeyaml/2.2/snakeyaml-2.2.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.2.0/spring-boot-starter-json-3.2.0.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.15.3/jackson-datatype-jdk8-2.15.3.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.15.3/jackson-datatype-jsr310-2.15.3.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.15.3/jackson-module-parameter-names-2.15.3.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.2.0/spring-boot-starter-tomcat-3.2.0.jar:/home/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.16/tomcat-embed-core-10.1.16.jar:/home/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.16/tomcat-embed-websocket-10.1.16.jar:/home/<USER>/.m2/repository/org/springframework/spring-web/6.1.1/spring-web-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/spring-beans/6.1.1/spring-beans-6.1.1.jar:/home/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.12.0/micrometer-observation-1.12.0.jar:/home/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.12.0/micrometer-commons-1.12.0.jar:/home/<USER>/.m2/repository/org/springframework/spring-webmvc/6.1.1/spring-webmvc-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/spring-context/6.1.1/spring-context-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/spring-expression/6.1.1/spring-expression-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/3.2.0/spring-boot-starter-data-jpa-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/3.2.0/spring-boot-starter-aop-3.2.0.jar:/home/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.20.1/aspectjweaver-1.9.20.1.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.2.0/spring-boot-starter-jdbc-3.2.0.jar:/home/<USER>/.m2/repository/com/zaxxer/HikariCP/5.0.1/HikariCP-5.0.1.jar:/home/<USER>/.m2/repository/org/springframework/spring-jdbc/6.1.1/spring-jdbc-6.1.1.jar:/home/<USER>/.m2/repository/org/hibernate/orm/hibernate-core/6.3.1.Final/hibernate-core-6.3.1.Final.jar:/home/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/3.1.0/jakarta.persistence-api-3.1.0.jar:/home/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar:/home/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.5.3.Final/jboss-logging-3.5.3.Final.jar:/home/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/6.0.6.Final/hibernate-commons-annotations-6.0.6.Final.jar:/home/<USER>/.m2/repository/io/smallrye/jandex/3.1.2/jandex-3.1.2.jar:/home/<USER>/.m2/repository/com/fasterxml/classmate/1.6.0/classmate-1.6.0.jar:/home/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/4.0.4/jaxb-runtime-4.0.4.jar:/home/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/4.0.4/jaxb-core-4.0.4.jar:/home/<USER>/.m2/repository/org/eclipse/angus/angus-activation/2.0.1/angus-activation-2.0.1.jar:/home/<USER>/.m2/repository/org/glassfish/jaxb/txw2/4.0.4/txw2-4.0.4.jar:/home/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar:/home/<USER>/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar:/home/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.10.1/antlr4-runtime-4.10.1.jar:/home/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/3.2.0/spring-data-jpa-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.2.0/spring-data-commons-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/spring-orm/6.1.1/spring-orm-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/spring-tx/6.1.1/spring-tx-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/spring-aspects/6.1.1/spring-aspects-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-thymeleaf/3.2.0/spring-boot-starter-thymeleaf-3.2.0.jar:/home/<USER>/.m2/repository/org/thymeleaf/thymeleaf-spring6/3.1.2.RELEASE/thymeleaf-spring6-3.1.2.RELEASE.jar:/home/<USER>/.m2/repository/org/thymeleaf/thymeleaf/3.1.2.RELEASE/thymeleaf-3.1.2.RELEASE.jar:/home/<USER>/.m2/repository/org/attoparser/attoparser/2.0.7.RELEASE/attoparser-2.0.7.RELEASE.jar:/home/<USER>/.m2/repository/org/unbescape/unbescape/1.1.6.RELEASE/unbescape-1.1.6.RELEASE.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.2.0/spring-boot-starter-security-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/spring-aop/6.1.1/spring-aop-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.2.0/spring-security-config-6.2.0.jar:/home/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.2.0/spring-security-web-6.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.2.0/spring-boot-starter-validation-3.2.0.jar:/home/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.16/tomcat-embed-el-10.1.16.jar:/home/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.1.Final/hibernate-validator-8.0.1.Final.jar:/home/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/home/<USER>/.m2/repository/org/postgresql/postgresql/42.6.0/postgresql-42.6.0.jar:/home/<USER>/.m2/repository/org/checkerframework/checker-qual/3.31.0/checker-qual-3.31.0.jar:/home/<USER>/.m2/repository/com/h2database/h2/2.2.224/h2-2.2.224.jar:/home/<USER>/.m2/repository/com/opencsv/opencsv/5.8/opencsv-5.8.jar:/home/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.13.0/commons-lang3-3.13.0.jar:/home/<USER>/.m2/repository/org/apache/commons/commons-text/1.10.0/commons-text-1.10.0.jar:/home/<USER>/.m2/repository/commons-beanutils/commons-beanutils/1.9.4/commons-beanutils-1.9.4.jar:/home/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/home/<USER>/.m2/repository/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.jar:/home/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.15.3/jackson-databind-2.15.3.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.15.3/jackson-annotations-2.15.3.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.15.3/jackson-core-2.15.3.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.2.0/spring-boot-starter-test-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.2.0/spring-boot-test-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.2.0/spring-boot-test-autoconfigure-3.2.0.jar:/home/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.8.0/json-path-2.8.0.jar:/home/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.1/jakarta.xml.bind-api-4.0.1.jar:/home/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.2/jakarta.activation-api-2.1.2.jar:/home/<USER>/.m2/repository/net/minidev/json-smart/2.5.0/json-smart-2.5.0.jar:/home/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.0/accessors-smart-2.5.0.jar:/home/<USER>/.m2/repository/org/awaitility/awaitility/4.2.0/awaitility-4.2.0.jar:/home/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.10.1/junit-jupiter-5.10.1.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.10.1/junit-jupiter-api-5.10.1.jar:/home/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/home/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.10.1/junit-platform-commons-1.10.1.jar:/home/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.10.1/junit-jupiter-params-5.10.1.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.10.1/junit-jupiter-engine-5.10.1.jar:/home/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.10.1/junit-platform-engine-1.10.1.jar:/home/<USER>/.m2/repository/org/mockito/mockito-core/5.7.0/mockito-core-5.7.0.jar:/home/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.14.10/byte-buddy-agent-1.14.10.jar:/home/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/home/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.7.0/mockito-junit-jupiter-5.7.0.jar:/home/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.1/jsonassert-1.5.1.jar:/home/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/home/<USER>/.m2/repository/org/springframework/spring-core/6.1.1/spring-core-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/spring-jcl/6.1.1/spring-jcl-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/spring-test/6.1.1/spring-test-6.1.1.jar:/home/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.9.1/xmlunit-core-2.9.1.jar:/home/<USER>/.m2/repository/org/springframework/security/spring-security-test/6.2.0/spring-security-test-6.2.0.jar:/home/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.2.0/spring-security-core-6.2.0.jar:/home/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.2.0/spring-security-crypto-6.2.0.jar:/home/<USER>/.m2/repository/org/assertj/assertj-core/3.24.2/assertj-core-3.24.2.jar:/home/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.14.10/byte-buddy-1.14.10.jar:/home/<USER>/.m2/repository/org/testcontainers/junit-jupiter/1.19.3/junit-jupiter-1.19.3.jar:/home/<USER>/.m2/repository/org/testcontainers/testcontainers/1.19.3/testcontainers-1.19.3.jar:/home/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/home/<USER>/.m2/repository/org/apache/commons/commons-compress/1.24.0/commons-compress-1.24.0.jar:/home/<USER>/.m2/repository/org/rnorth/duct-tape/duct-tape/1.0.8/duct-tape-1.0.8.jar:/home/<USER>/.m2/repository/org/jetbrains/annotations/17.0.0/annotations-17.0.0.jar:/home/<USER>/.m2/repository/com/github/docker-java/docker-java-api/3.3.4/docker-java-api-3.3.4.jar:/home/<USER>/.m2/repository/com/github/docker-java/docker-java-transport-zerodep/3.3.4/docker-java-transport-zerodep-3.3.4.jar:/home/<USER>/.m2/repository/com/github/docker-java/docker-java-transport/3.3.4/docker-java-transport-3.3.4.jar:/home/<USER>/.m2/repository/net/java/dev/jna/jna/5.13.0/jna-5.13.0.jar:/home/<USER>/.m2/repository/org/testcontainers/postgresql/1.19.3/postgresql-1.19.3.jar:/home/<USER>/.m2/repository/org/testcontainers/jdbc/1.19.3/jdbc-1.19.3.jar:/home/<USER>/.m2/repository/org/testcontainers/database-commons/1.19.3/database-commons-1.19.3.jar:/home/<USER>/.m2/repository/com/github/tomakehurst/wiremock-jre8/2.35.0/wiremock-jre8-2.35.0.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-server/12.0.3/jetty-server-12.0.3.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-http/12.0.3/jetty-http-12.0.3.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-io/12.0.3/jetty-io-12.0.3.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-servlet/9.4.49.v20220914/jetty-servlet-9.4.49.v20220914.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-security/12.0.3/jetty-security-12.0.3.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-util-ajax/12.0.3/jetty-util-ajax-12.0.3.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-servlets/9.4.49.v20220914/jetty-servlets-9.4.49.v20220914.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-continuation/9.4.49.v20220914/jetty-continuation-9.4.49.v20220914.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-util/12.0.3/jetty-util-12.0.3.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-webapp/9.4.49.v20220914/jetty-webapp-9.4.49.v20220914.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-xml/12.0.3/jetty-xml-12.0.3.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-proxy/12.0.3/jetty-proxy-12.0.3.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-client/12.0.3/jetty-client-12.0.3.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/http2/http2-server/9.4.49.v20220914/http2-server-9.4.49.v20220914.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/http2/http2-common/9.4.49.v20220914/http2-common-9.4.49.v20220914.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/http2/http2-hpack/9.4.49.v20220914/http2-hpack-9.4.49.v20220914.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-alpn-server/12.0.3/jetty-alpn-server-12.0.3.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-alpn-java-server/12.0.3/jetty-alpn-java-server-12.0.3.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-alpn-openjdk8-server/9.4.49.v20220914/jetty-alpn-openjdk8-server-9.4.49.v20220914.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-alpn-java-client/12.0.3/jetty-alpn-java-client-12.0.3.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-alpn-client/12.0.3/jetty-alpn-client-12.0.3.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-alpn-openjdk8-client/9.4.49.v20220914/jetty-alpn-openjdk8-client-9.4.49.v20220914.jar:/home/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/home/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/home/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/home/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/home/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/home/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/home/<USER>/.m2/repository/org/apache/httpcomponents/client5/httpclient5/5.2.1/httpclient5-5.2.1.jar:/home/<USER>/.m2/repository/org/apache/httpcomponents/core5/httpcore5/5.2.3/httpcore5-5.2.3.jar:/home/<USER>/.m2/repository/org/apache/httpcomponents/core5/httpcore5-h2/5.2.3/httpcore5-h2-5.2.3.jar:/home/<USER>/.m2/repository/org/xmlunit/xmlunit-legacy/2.9.1/xmlunit-legacy-2.9.1.jar:/home/<USER>/.m2/repository/org/xmlunit/xmlunit-placeholders/2.9.1/xmlunit-placeholders-2.9.1.jar:/home/<USER>/.m2/repository/net/javacrumbs/json-unit/json-unit-core/2.36.0/json-unit-core-2.36.0.jar:/home/<USER>/.m2/repository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/home/<USER>/.m2/repository/org/ow2/asm/asm/9.4/asm-9.4.jar:/home/<USER>/.m2/repository/net/sf/jopt-simple/jopt-simple/5.0.4/jopt-simple-5.0.4.jar:/home/<USER>/.m2/repository/com/github/jknack/handlebars/4.3.1/handlebars-4.3.1.jar:/home/<USER>/.m2/repository/com/github/jknack/handlebars-helpers/4.3.1/handlebars-helpers-4.3.1.jar:/home/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.4/commons-fileupload-1.4.jar:/home/<USER>/.m2/repository/commons-io/commons-io/2.11.0/commons-io-2.11.0.jar:/home/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.9/slf4j-api-2.0.9.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-devtools/3.2.0/spring-boot-devtools-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.2.0/spring-boot-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.2.0/spring-boot-autoconfigure-3.2.0.jar:"/>
    <property name="java.vm.vendor" value="Ubuntu"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="java.vendor.url" value="https://ubuntu.com/"/>
    <property name="user.timezone" value="Asia/Kolkata"/>
    <property name="os.name" value="Linux"/>
    <property name="java.vm.specification.version" value="21"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="US"/>
    <property name="sun.boot.library.path" value="/usr/lib/jvm/java-21-openjdk-amd64/lib"/>
    <property name="sun.java.command" value="/home/<USER>/repo/sd-customer-success/target/surefire/surefirebooter-20250914190856170_3.jar /home/<USER>/repo/sd-customer-success/target/surefire 2025-09-14T19-08-50_806-jvmRun1 surefire-20250914190856170_1tmp surefire_0-20250914190856170_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="test" value="UserUsageV2CsvProcessorTest"/>
    <property name="surefire.test.class.path" value="/home/<USER>/repo/sd-customer-success/target/test-classes:/home/<USER>/repo/sd-customer-success/target/classes:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/3.2.0/spring-boot-starter-web-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/3.2.0/spring-boot-starter-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/3.2.0/spring-boot-starter-logging-3.2.0.jar:/home/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.4.11/logback-classic-1.4.11.jar:/home/<USER>/.m2/repository/ch/qos/logback/logback-core/1.4.11/logback-core-1.4.11.jar:/home/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.21.1/log4j-to-slf4j-2.21.1.jar:/home/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.21.1/log4j-api-2.21.1.jar:/home/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/2.0.9/jul-to-slf4j-2.0.9.jar:/home/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/2.1.1/jakarta.annotation-api-2.1.1.jar:/home/<USER>/.m2/repository/org/yaml/snakeyaml/2.2/snakeyaml-2.2.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/3.2.0/spring-boot-starter-json-3.2.0.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.15.3/jackson-datatype-jdk8-2.15.3.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.15.3/jackson-datatype-jsr310-2.15.3.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.15.3/jackson-module-parameter-names-2.15.3.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/3.2.0/spring-boot-starter-tomcat-3.2.0.jar:/home/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/10.1.16/tomcat-embed-core-10.1.16.jar:/home/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/10.1.16/tomcat-embed-websocket-10.1.16.jar:/home/<USER>/.m2/repository/org/springframework/spring-web/6.1.1/spring-web-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/spring-beans/6.1.1/spring-beans-6.1.1.jar:/home/<USER>/.m2/repository/io/micrometer/micrometer-observation/1.12.0/micrometer-observation-1.12.0.jar:/home/<USER>/.m2/repository/io/micrometer/micrometer-commons/1.12.0/micrometer-commons-1.12.0.jar:/home/<USER>/.m2/repository/org/springframework/spring-webmvc/6.1.1/spring-webmvc-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/spring-context/6.1.1/spring-context-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/spring-expression/6.1.1/spring-expression-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-data-jpa/3.2.0/spring-boot-starter-data-jpa-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-aop/3.2.0/spring-boot-starter-aop-3.2.0.jar:/home/<USER>/.m2/repository/org/aspectj/aspectjweaver/1.9.20.1/aspectjweaver-1.9.20.1.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/3.2.0/spring-boot-starter-jdbc-3.2.0.jar:/home/<USER>/.m2/repository/com/zaxxer/HikariCP/5.0.1/HikariCP-5.0.1.jar:/home/<USER>/.m2/repository/org/springframework/spring-jdbc/6.1.1/spring-jdbc-6.1.1.jar:/home/<USER>/.m2/repository/org/hibernate/orm/hibernate-core/6.3.1.Final/hibernate-core-6.3.1.Final.jar:/home/<USER>/.m2/repository/jakarta/persistence/jakarta.persistence-api/3.1.0/jakarta.persistence-api-3.1.0.jar:/home/<USER>/.m2/repository/jakarta/transaction/jakarta.transaction-api/2.0.1/jakarta.transaction-api-2.0.1.jar:/home/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.5.3.Final/jboss-logging-3.5.3.Final.jar:/home/<USER>/.m2/repository/org/hibernate/common/hibernate-commons-annotations/6.0.6.Final/hibernate-commons-annotations-6.0.6.Final.jar:/home/<USER>/.m2/repository/io/smallrye/jandex/3.1.2/jandex-3.1.2.jar:/home/<USER>/.m2/repository/com/fasterxml/classmate/1.6.0/classmate-1.6.0.jar:/home/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-runtime/4.0.4/jaxb-runtime-4.0.4.jar:/home/<USER>/.m2/repository/org/glassfish/jaxb/jaxb-core/4.0.4/jaxb-core-4.0.4.jar:/home/<USER>/.m2/repository/org/eclipse/angus/angus-activation/2.0.1/angus-activation-2.0.1.jar:/home/<USER>/.m2/repository/org/glassfish/jaxb/txw2/4.0.4/txw2-4.0.4.jar:/home/<USER>/.m2/repository/com/sun/istack/istack-commons-runtime/4.1.2/istack-commons-runtime-4.1.2.jar:/home/<USER>/.m2/repository/jakarta/inject/jakarta.inject-api/2.0.1/jakarta.inject-api-2.0.1.jar:/home/<USER>/.m2/repository/org/antlr/antlr4-runtime/4.10.1/antlr4-runtime-4.10.1.jar:/home/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/3.2.0/spring-data-jpa-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/data/spring-data-commons/3.2.0/spring-data-commons-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/spring-orm/6.1.1/spring-orm-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/spring-tx/6.1.1/spring-tx-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/spring-aspects/6.1.1/spring-aspects-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-thymeleaf/3.2.0/spring-boot-starter-thymeleaf-3.2.0.jar:/home/<USER>/.m2/repository/org/thymeleaf/thymeleaf-spring6/3.1.2.RELEASE/thymeleaf-spring6-3.1.2.RELEASE.jar:/home/<USER>/.m2/repository/org/thymeleaf/thymeleaf/3.1.2.RELEASE/thymeleaf-3.1.2.RELEASE.jar:/home/<USER>/.m2/repository/org/attoparser/attoparser/2.0.7.RELEASE/attoparser-2.0.7.RELEASE.jar:/home/<USER>/.m2/repository/org/unbescape/unbescape/1.1.6.RELEASE/unbescape-1.1.6.RELEASE.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-security/3.2.0/spring-boot-starter-security-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/spring-aop/6.1.1/spring-aop-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/security/spring-security-config/6.2.0/spring-security-config-6.2.0.jar:/home/<USER>/.m2/repository/org/springframework/security/spring-security-web/6.2.0/spring-security-web-6.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-validation/3.2.0/spring-boot-starter-validation-3.2.0.jar:/home/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/10.1.16/tomcat-embed-el-10.1.16.jar:/home/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/8.0.1.Final/hibernate-validator-8.0.1.Final.jar:/home/<USER>/.m2/repository/jakarta/validation/jakarta.validation-api/3.0.2/jakarta.validation-api-3.0.2.jar:/home/<USER>/.m2/repository/org/postgresql/postgresql/42.6.0/postgresql-42.6.0.jar:/home/<USER>/.m2/repository/org/checkerframework/checker-qual/3.31.0/checker-qual-3.31.0.jar:/home/<USER>/.m2/repository/com/h2database/h2/2.2.224/h2-2.2.224.jar:/home/<USER>/.m2/repository/com/opencsv/opencsv/5.8/opencsv-5.8.jar:/home/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.13.0/commons-lang3-3.13.0.jar:/home/<USER>/.m2/repository/org/apache/commons/commons-text/1.10.0/commons-text-1.10.0.jar:/home/<USER>/.m2/repository/commons-beanutils/commons-beanutils/1.9.4/commons-beanutils-1.9.4.jar:/home/<USER>/.m2/repository/commons-logging/commons-logging/1.2/commons-logging-1.2.jar:/home/<USER>/.m2/repository/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.jar:/home/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.15.3/jackson-databind-2.15.3.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.15.3/jackson-annotations-2.15.3.jar:/home/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.15.3/jackson-core-2.15.3.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-test/3.2.0/spring-boot-starter-test-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-test/3.2.0/spring-boot-test-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-test-autoconfigure/3.2.0/spring-boot-test-autoconfigure-3.2.0.jar:/home/<USER>/.m2/repository/com/jayway/jsonpath/json-path/2.8.0/json-path-2.8.0.jar:/home/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/4.0.1/jakarta.xml.bind-api-4.0.1.jar:/home/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/2.1.2/jakarta.activation-api-2.1.2.jar:/home/<USER>/.m2/repository/net/minidev/json-smart/2.5.0/json-smart-2.5.0.jar:/home/<USER>/.m2/repository/net/minidev/accessors-smart/2.5.0/accessors-smart-2.5.0.jar:/home/<USER>/.m2/repository/org/awaitility/awaitility/4.2.0/awaitility-4.2.0.jar:/home/<USER>/.m2/repository/org/hamcrest/hamcrest/2.2/hamcrest-2.2.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter/5.10.1/junit-jupiter-5.10.1.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-api/5.10.1/junit-jupiter-api-5.10.1.jar:/home/<USER>/.m2/repository/org/opentest4j/opentest4j/1.3.0/opentest4j-1.3.0.jar:/home/<USER>/.m2/repository/org/junit/platform/junit-platform-commons/1.10.1/junit-platform-commons-1.10.1.jar:/home/<USER>/.m2/repository/org/apiguardian/apiguardian-api/1.1.2/apiguardian-api-1.1.2.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-params/5.10.1/junit-jupiter-params-5.10.1.jar:/home/<USER>/.m2/repository/org/junit/jupiter/junit-jupiter-engine/5.10.1/junit-jupiter-engine-5.10.1.jar:/home/<USER>/.m2/repository/org/junit/platform/junit-platform-engine/1.10.1/junit-platform-engine-1.10.1.jar:/home/<USER>/.m2/repository/org/mockito/mockito-core/5.7.0/mockito-core-5.7.0.jar:/home/<USER>/.m2/repository/net/bytebuddy/byte-buddy-agent/1.14.10/byte-buddy-agent-1.14.10.jar:/home/<USER>/.m2/repository/org/objenesis/objenesis/3.3/objenesis-3.3.jar:/home/<USER>/.m2/repository/org/mockito/mockito-junit-jupiter/5.7.0/mockito-junit-jupiter-5.7.0.jar:/home/<USER>/.m2/repository/org/skyscreamer/jsonassert/1.5.1/jsonassert-1.5.1.jar:/home/<USER>/.m2/repository/com/vaadin/external/google/android-json/0.0.20131108.vaadin1/android-json-0.0.20131108.vaadin1.jar:/home/<USER>/.m2/repository/org/springframework/spring-core/6.1.1/spring-core-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/spring-jcl/6.1.1/spring-jcl-6.1.1.jar:/home/<USER>/.m2/repository/org/springframework/spring-test/6.1.1/spring-test-6.1.1.jar:/home/<USER>/.m2/repository/org/xmlunit/xmlunit-core/2.9.1/xmlunit-core-2.9.1.jar:/home/<USER>/.m2/repository/org/springframework/security/spring-security-test/6.2.0/spring-security-test-6.2.0.jar:/home/<USER>/.m2/repository/org/springframework/security/spring-security-core/6.2.0/spring-security-core-6.2.0.jar:/home/<USER>/.m2/repository/org/springframework/security/spring-security-crypto/6.2.0/spring-security-crypto-6.2.0.jar:/home/<USER>/.m2/repository/org/assertj/assertj-core/3.24.2/assertj-core-3.24.2.jar:/home/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.14.10/byte-buddy-1.14.10.jar:/home/<USER>/.m2/repository/org/testcontainers/junit-jupiter/1.19.3/junit-jupiter-1.19.3.jar:/home/<USER>/.m2/repository/org/testcontainers/testcontainers/1.19.3/testcontainers-1.19.3.jar:/home/<USER>/.m2/repository/junit/junit/4.13.2/junit-4.13.2.jar:/home/<USER>/.m2/repository/org/apache/commons/commons-compress/1.24.0/commons-compress-1.24.0.jar:/home/<USER>/.m2/repository/org/rnorth/duct-tape/duct-tape/1.0.8/duct-tape-1.0.8.jar:/home/<USER>/.m2/repository/org/jetbrains/annotations/17.0.0/annotations-17.0.0.jar:/home/<USER>/.m2/repository/com/github/docker-java/docker-java-api/3.3.4/docker-java-api-3.3.4.jar:/home/<USER>/.m2/repository/com/github/docker-java/docker-java-transport-zerodep/3.3.4/docker-java-transport-zerodep-3.3.4.jar:/home/<USER>/.m2/repository/com/github/docker-java/docker-java-transport/3.3.4/docker-java-transport-3.3.4.jar:/home/<USER>/.m2/repository/net/java/dev/jna/jna/5.13.0/jna-5.13.0.jar:/home/<USER>/.m2/repository/org/testcontainers/postgresql/1.19.3/postgresql-1.19.3.jar:/home/<USER>/.m2/repository/org/testcontainers/jdbc/1.19.3/jdbc-1.19.3.jar:/home/<USER>/.m2/repository/org/testcontainers/database-commons/1.19.3/database-commons-1.19.3.jar:/home/<USER>/.m2/repository/com/github/tomakehurst/wiremock-jre8/2.35.0/wiremock-jre8-2.35.0.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-server/12.0.3/jetty-server-12.0.3.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-http/12.0.3/jetty-http-12.0.3.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-io/12.0.3/jetty-io-12.0.3.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-servlet/9.4.49.v20220914/jetty-servlet-9.4.49.v20220914.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-security/12.0.3/jetty-security-12.0.3.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-util-ajax/12.0.3/jetty-util-ajax-12.0.3.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-servlets/9.4.49.v20220914/jetty-servlets-9.4.49.v20220914.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-continuation/9.4.49.v20220914/jetty-continuation-9.4.49.v20220914.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-util/12.0.3/jetty-util-12.0.3.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-webapp/9.4.49.v20220914/jetty-webapp-9.4.49.v20220914.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-xml/12.0.3/jetty-xml-12.0.3.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-proxy/12.0.3/jetty-proxy-12.0.3.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-client/12.0.3/jetty-client-12.0.3.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/http2/http2-server/9.4.49.v20220914/http2-server-9.4.49.v20220914.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/http2/http2-common/9.4.49.v20220914/http2-common-9.4.49.v20220914.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/http2/http2-hpack/9.4.49.v20220914/http2-hpack-9.4.49.v20220914.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-alpn-server/12.0.3/jetty-alpn-server-12.0.3.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-alpn-java-server/12.0.3/jetty-alpn-java-server-12.0.3.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-alpn-openjdk8-server/9.4.49.v20220914/jetty-alpn-openjdk8-server-9.4.49.v20220914.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-alpn-java-client/12.0.3/jetty-alpn-java-client-12.0.3.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-alpn-client/12.0.3/jetty-alpn-client-12.0.3.jar:/home/<USER>/.m2/repository/org/eclipse/jetty/jetty-alpn-openjdk8-client/9.4.49.v20220914/jetty-alpn-openjdk8-client-9.4.49.v20220914.jar:/home/<USER>/.m2/repository/com/google/guava/guava/31.1-jre/guava-31.1-jre.jar:/home/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar:/home/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/home/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/home/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.11.0/error_prone_annotations-2.11.0.jar:/home/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar:/home/<USER>/.m2/repository/org/apache/httpcomponents/client5/httpclient5/5.2.1/httpclient5-5.2.1.jar:/home/<USER>/.m2/repository/org/apache/httpcomponents/core5/httpcore5/5.2.3/httpcore5-5.2.3.jar:/home/<USER>/.m2/repository/org/apache/httpcomponents/core5/httpcore5-h2/5.2.3/httpcore5-h2-5.2.3.jar:/home/<USER>/.m2/repository/org/xmlunit/xmlunit-legacy/2.9.1/xmlunit-legacy-2.9.1.jar:/home/<USER>/.m2/repository/org/xmlunit/xmlunit-placeholders/2.9.1/xmlunit-placeholders-2.9.1.jar:/home/<USER>/.m2/repository/net/javacrumbs/json-unit/json-unit-core/2.36.0/json-unit-core-2.36.0.jar:/home/<USER>/.m2/repository/org/hamcrest/hamcrest-core/2.2/hamcrest-core-2.2.jar:/home/<USER>/.m2/repository/org/ow2/asm/asm/9.4/asm-9.4.jar:/home/<USER>/.m2/repository/net/sf/jopt-simple/jopt-simple/5.0.4/jopt-simple-5.0.4.jar:/home/<USER>/.m2/repository/com/github/jknack/handlebars/4.3.1/handlebars-4.3.1.jar:/home/<USER>/.m2/repository/com/github/jknack/handlebars-helpers/4.3.1/handlebars-helpers-4.3.1.jar:/home/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.4/commons-fileupload-1.4.jar:/home/<USER>/.m2/repository/commons-io/commons-io/2.11.0/commons-io-2.11.0.jar:/home/<USER>/.m2/repository/org/slf4j/slf4j-api/2.0.9/slf4j-api-2.0.9.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-devtools/3.2.0/spring-boot-devtools-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot/3.2.0/spring-boot-3.2.0.jar:/home/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/3.2.0/spring-boot-autoconfigure-3.2.0.jar:"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="/home/<USER>"/>
    <property name="user.language" value="en"/>
    <property name="java.specification.vendor" value="Oracle Corporation"/>
    <property name="java.version.date" value="2025-07-15"/>
    <property name="java.home" value="/usr/lib/jvm/java-21-openjdk-amd64"/>
    <property name="file.separator" value="/"/>
    <property name="basedir" value="/home/<USER>/repo/sd-customer-success"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="surefire.real.class.path" value="/home/<USER>/repo/sd-customer-success/target/surefire/surefirebooter-20250914190856170_3.jar"/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="21.0.8+9-Ubuntu-0ubuntu124.04.1"/>
    <property name="user.name" value="alpesh"/>
    <property name="stdout.encoding" value="UTF-8"/>
    <property name="path.separator" value=":"/>
    <property name="os.version" value="6.8.0-79-generic"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="localRepository" value="/home/<USER>/.m2/repository"/>
    <property name="java.vendor.url.bug" value="https://bugs.launchpad.net/ubuntu/+source/openjdk-21"/>
    <property name="java.io.tmpdir" value="/tmp"/>
    <property name="java.version" value="21.0.8"/>
    <property name="user.dir" value="/home/<USER>/repo/sd-customer-success"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="native.encoding" value="UTF-8"/>
    <property name="java.library.path" value="/usr/java/packages/lib:/usr/lib/x86_64-linux-gnu/jni:/lib/x86_64-linux-gnu:/usr/lib/x86_64-linux-gnu:/usr/lib/jni:/lib:/usr/lib"/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="UTF-8"/>
    <property name="java.vendor" value="Ubuntu"/>
    <property name="java.vm.version" value="21.0.8+9-Ubuntu-0ubuntu124.04.1"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="65.0"/>
  </properties>
  <testcase name="testProcessCsvFile_WithOptionalCoreColumns_ShouldHandleNulls" classname="com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest" time="0.386">
    <system-out><![CDATA[19:08:57.056 [main] INFO com.kylas.analytics.service.csv.UserUsageV2CsvProcessor -- Processing CSV file: /tmp/junit1754532180331251045/optional-nulls.csv
19:08:57.078 [main] INFO com.kylas.analytics.service.csv.UserUsageV2CsvProcessor -- Successfully processed 1 user usage records
]]></system-out>
  </testcase>
  <testcase name="testProcessCsvFile_WithEmptyRequiredFields_ShouldThrowException" classname="com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest" time="0.029">
    <failure message="&#10;Expecting throwable message:&#10;  &quot;Failed to process record at row 2&quot;&#10;to contain:&#10;  &quot;Required column value is empty&quot;&#10;but did not.&#10;&#10;Throwable that failed the check:&#10;&#10;com.kylas.analytics.service.csv.CsvProcessingException: Failed to process record at row 2&#10;	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessor.processCsvFile(UserUsageV2CsvProcessor.java:80)&#10;	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest.lambda$testProcessCsvFile_WithEmptyRequiredFields_ShouldThrowException$1(UserUsageV2CsvProcessorTest.java:88)&#10;	at org.assertj.core.api.ThrowableAssert.catchThrowable(ThrowableAssert.java:63)&#10;	at org.assertj.core.api.AssertionsForClassTypes.catchThrowable(AssertionsForClassTypes.java:892)&#10;	at org.assertj.core.api.Assertions.catchThrowable(Assertions.java:1366)&#10;	at org.assertj.core.api.Assertions.assertThatThrownBy(Assertions.java:1210)&#10;	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest.testProcessCsvFile_WithEmptyRequiredFields_ShouldThrowException(UserUsageV2CsvProcessorTest.java:88)&#10;	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)&#10;	at java.base/java.lang.reflect.Method.invoke(Method.java:580)&#10;	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:728)&#10;	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)&#10;	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)&#10;	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)&#10;	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)&#10;	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)&#10;	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)&#10;	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)&#10;	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)&#10;	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)&#10;	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)&#10;	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)&#10;	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)&#10;	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)&#10;	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:218)&#10;	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)&#10;	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:214)&#10;	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:139)&#10;	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)&#10;	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)&#10;	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)&#10;	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)&#10;	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)&#10;	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)&#10;	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)&#10;	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)&#10;	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)&#10;	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)&#10;	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)&#10;	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)&#10;	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)&#10;	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)&#10;	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)&#10;	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)&#10;	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)&#10;	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)&#10;	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)&#10;	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)&#10;	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)&#10;	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)&#10;	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)&#10;	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)&#10;	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)&#10;	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)&#10;	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:55)&#10;	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:223)&#10;	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:175)&#10;	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:139)&#10;	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)&#10;	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)&#10;	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)&#10;	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)&#10;Caused by: com.kylas.analytics.service.csv.CsvProcessingException: Required column value is empty: userId&#10;	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessor.getStringValue(UserUsageV2CsvProcessor.java:300)&#10;	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessor.processCoreColumns(UserUsageV2CsvProcessor.java:192)&#10;	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessor.processRow(UserUsageV2CsvProcessor.java:174)&#10;	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessor.processCsvFile(UserUsageV2CsvProcessor.java:74)&#10;	... 75 more&#10;" type="java.lang.AssertionError"><![CDATA[java.lang.AssertionError: 

Expecting throwable message:
  "Failed to process record at row 2"
to contain:
  "Required column value is empty"
but did not.

Throwable that failed the check:

com.kylas.analytics.service.csv.CsvProcessingException: Failed to process record at row 2
	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessor.processCsvFile(UserUsageV2CsvProcessor.java:80)
	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest.lambda$testProcessCsvFile_WithEmptyRequiredFields_ShouldThrowException$1(UserUsageV2CsvProcessorTest.java:88)
	at org.assertj.core.api.ThrowableAssert.catchThrowable(ThrowableAssert.java:63)
	at org.assertj.core.api.AssertionsForClassTypes.catchThrowable(AssertionsForClassTypes.java:892)
	at org.assertj.core.api.Assertions.catchThrowable(Assertions.java:1366)
	at org.assertj.core.api.Assertions.assertThatThrownBy(Assertions.java:1210)
	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest.testProcessCsvFile_WithEmptyRequiredFields_ShouldThrowException(UserUsageV2CsvProcessorTest.java:88)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:728)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:218)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:214)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:139)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:55)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:223)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:175)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:139)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)
Caused by: com.kylas.analytics.service.csv.CsvProcessingException: Required column value is empty: userId
	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessor.getStringValue(UserUsageV2CsvProcessor.java:300)
	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessor.processCoreColumns(UserUsageV2CsvProcessor.java:192)
	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessor.processRow(UserUsageV2CsvProcessor.java:174)
	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessor.processCsvFile(UserUsageV2CsvProcessor.java:74)
	... 75 more

	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest.testProcessCsvFile_WithEmptyRequiredFields_ShouldThrowException(UserUsageV2CsvProcessorTest.java:90)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
]]></failure>
    <system-out><![CDATA[19:08:57.139 [main] INFO com.kylas.analytics.service.csv.UserUsageV2CsvProcessor -- Processing CSV file: /tmp/junit17375994165680128050/empty-required.csv
19:08:57.140 [main] ERROR com.kylas.analytics.service.csv.UserUsageV2CsvProcessor -- Error processing row 2: Required column value is empty: userId
]]></system-out>
  </testcase>
  <testcase name="testValidateCsvFile_WithMissingRequiredColumns_ShouldReturnFalse" classname="com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest" time="0.006">
    <system-out><![CDATA[19:08:57.180 [main] WARN com.kylas.analytics.service.csv.UserUsageV2CsvProcessor -- Missing required column 'tenantId' in CSV file: /tmp/junit5032944082696256939/invalid-validation.csv
]]></system-out>
  </testcase>
  <testcase name="testProcessCsvFile_WithMissingRequiredColumns_ShouldThrowException" classname="com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest" time="0.011">
    <failure message="&#10;Expecting throwable message:&#10;  &quot;CSV must have at least 5 columns&quot;&#10;to contain:&#10;  &quot;Missing required column&quot;&#10;but did not.&#10;&#10;Throwable that failed the check:&#10;&#10;com.kylas.analytics.service.csv.CsvProcessingException: CSV must have at least 5 columns&#10;	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessor.validateHeaders(UserUsageV2CsvProcessor.java:140)&#10;	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessor.processCsvFile(UserUsageV2CsvProcessor.java:66)&#10;	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest.lambda$testProcessCsvFile_WithMissingRequiredColumns_ShouldThrowException$0(UserUsageV2CsvProcessorTest.java:75)&#10;	at org.assertj.core.api.ThrowableAssert.catchThrowable(ThrowableAssert.java:63)&#10;	at org.assertj.core.api.AssertionsForClassTypes.catchThrowable(AssertionsForClassTypes.java:892)&#10;	at org.assertj.core.api.Assertions.catchThrowable(Assertions.java:1366)&#10;	at org.assertj.core.api.Assertions.assertThatThrownBy(Assertions.java:1210)&#10;	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest.testProcessCsvFile_WithMissingRequiredColumns_ShouldThrowException(UserUsageV2CsvProcessorTest.java:75)&#10;	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)&#10;	at java.base/java.lang.reflect.Method.invoke(Method.java:580)&#10;	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:728)&#10;	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)&#10;	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)&#10;	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)&#10;	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)&#10;	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)&#10;	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)&#10;	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)&#10;	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)&#10;	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)&#10;	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)&#10;	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)&#10;	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)&#10;	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)&#10;	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:218)&#10;	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)&#10;	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:214)&#10;	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:139)&#10;	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)&#10;	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)&#10;	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)&#10;	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)&#10;	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)&#10;	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)&#10;	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)&#10;	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)&#10;	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)&#10;	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)&#10;	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)&#10;	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)&#10;	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)&#10;	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)&#10;	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)&#10;	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)&#10;	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)&#10;	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)&#10;	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)&#10;	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)&#10;	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)&#10;	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)&#10;	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)&#10;	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)&#10;	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)&#10;	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)&#10;	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:55)&#10;	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:223)&#10;	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:175)&#10;	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:139)&#10;	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)&#10;	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)&#10;	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)&#10;	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)&#10;" type="java.lang.AssertionError"><![CDATA[java.lang.AssertionError: 

Expecting throwable message:
  "CSV must have at least 5 columns"
to contain:
  "Missing required column"
but did not.

Throwable that failed the check:

com.kylas.analytics.service.csv.CsvProcessingException: CSV must have at least 5 columns
	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessor.validateHeaders(UserUsageV2CsvProcessor.java:140)
	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessor.processCsvFile(UserUsageV2CsvProcessor.java:66)
	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest.lambda$testProcessCsvFile_WithMissingRequiredColumns_ShouldThrowException$0(UserUsageV2CsvProcessorTest.java:75)
	at org.assertj.core.api.ThrowableAssert.catchThrowable(ThrowableAssert.java:63)
	at org.assertj.core.api.AssertionsForClassTypes.catchThrowable(AssertionsForClassTypes.java:892)
	at org.assertj.core.api.Assertions.catchThrowable(Assertions.java:1366)
	at org.assertj.core.api.Assertions.assertThatThrownBy(Assertions.java:1210)
	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest.testProcessCsvFile_WithMissingRequiredColumns_ShouldThrowException(UserUsageV2CsvProcessorTest.java:75)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:728)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:218)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:214)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:139)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:55)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:223)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:175)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:139)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)

	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest.testProcessCsvFile_WithMissingRequiredColumns_ShouldThrowException(UserUsageV2CsvProcessorTest.java:77)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
]]></failure>
    <system-out><![CDATA[19:08:57.191 [main] INFO com.kylas.analytics.service.csv.UserUsageV2CsvProcessor -- Processing CSV file: /tmp/junit11207720975919928233/missing-columns.csv
]]></system-out>
  </testcase>
  <testcase name="testGetEntityClass_ShouldReturnUserUsageV2Class" classname="com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest" time="0.007"/>
  <testcase name="testProcessCsvFile_WithSpecialCharacters_ShouldHandleCorrectly" classname="com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest" time="0.005">
    <system-out><![CDATA[19:08:57.216 [main] INFO com.kylas.analytics.service.csv.UserUsageV2CsvProcessor -- Processing CSV file: /tmp/junit10091519800488232848/special-chars.csv
19:08:57.218 [main] INFO com.kylas.analytics.service.csv.UserUsageV2CsvProcessor -- Successfully processed 1 user usage records
]]></system-out>
  </testcase>
  <testcase name="testProcessCsvFile_WithEmptyAdditionalColumns_ShouldSkipEmptyValues" classname="com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest" time="0.004">
    <system-out><![CDATA[19:08:57.223 [main] INFO com.kylas.analytics.service.csv.UserUsageV2CsvProcessor -- Processing CSV file: /tmp/junit8240265348602911724/empty-additional.csv
19:08:57.223 [main] INFO com.kylas.analytics.service.csv.UserUsageV2CsvProcessor -- Successfully processed 1 user usage records
]]></system-out>
  </testcase>
  <testcase name="testValidateCsvFile_WithHeaderOnly_ShouldReturnFalse" classname="com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest" time="0.003">
    <system-out><![CDATA[19:08:57.229 [main] WARN com.kylas.analytics.service.csv.UserUsageV2CsvProcessor -- CSV file has no data rows: /tmp/junit1585562657512353152/header-only.csv
]]></system-out>
  </testcase>
  <testcase name="testProcessCsvFile_WithLargeDataset_ShouldHandleEfficiently" classname="com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest" time="0.091">
    <system-out><![CDATA[19:08:57.257 [main] INFO com.kylas.analytics.service.csv.UserUsageV2CsvProcessor -- Processing CSV file: /tmp/junit4220493266119844632/large-dataset.csv
19:08:57.315 [main] INFO com.kylas.analytics.service.csv.UserUsageV2CsvProcessor -- Successfully processed 1000 user usage records
]]></system-out>
  </testcase>
  <testcase name="testProcessCsvFile_WithVariousDataTypes_ShouldParseCorrectly" classname="com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest" time="0.006">
    <system-out><![CDATA[19:08:57.325 [main] INFO com.kylas.analytics.service.csv.UserUsageV2CsvProcessor -- Processing CSV file: /tmp/junit8382064610506037205/data-types.csv
19:08:57.326 [main] INFO com.kylas.analytics.service.csv.UserUsageV2CsvProcessor -- Successfully processed 1 user usage records
]]></system-out>
  </testcase>
  <testcase name="testProcessCsvFile_WithMultipleDateFormats_ShouldParseAll" classname="com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest" time="0.007">
    <failure message="&#10;Expecting actual not to be null" type="java.lang.AssertionError"><![CDATA[java.lang.AssertionError: 

Expecting actual not to be null
	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest.testProcessCsvFile_WithMultipleDateFormats_ShouldParseAll(UserUsageV2CsvProcessorTest.java:180)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
]]></failure>
    <system-out><![CDATA[19:08:57.332 [main] INFO com.kylas.analytics.service.csv.UserUsageV2CsvProcessor -- Processing CSV file: /tmp/junit17615697954272794476/date-formats.csv
19:08:57.333 [main] WARN com.kylas.analytics.service.csv.UserUsageV2CsvProcessor -- Could not parse date value '2023-01-01' for column 'updatedAt'
19:08:57.333 [main] INFO com.kylas.analytics.service.csv.UserUsageV2CsvProcessor -- Successfully processed 1 user usage records
]]></system-out>
  </testcase>
  <testcase name="testProcessCsvFile_WithValidData_ShouldCreateUserUsageEntities" classname="com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest" time="0.011">
    <system-out><![CDATA[19:08:57.345 [main] INFO com.kylas.analytics.service.csv.UserUsageV2CsvProcessor -- Processing CSV file: /tmp/junit3990120346787757922/valid-data.csv
19:08:57.346 [main] INFO com.kylas.analytics.service.csv.UserUsageV2CsvProcessor -- Successfully processed 2 user usage records
]]></system-out>
  </testcase>
  <testcase name="testProcessCsvFile_WithInvalidDateFormat_ShouldThrowException" classname="com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest" time="0.012">
    <failure message="&#10;Expecting throwable message:&#10;  &quot;Failed to process record at row 2&quot;&#10;to contain:&#10;  &quot;Invalid date format&quot;&#10;but did not.&#10;&#10;Throwable that failed the check:&#10;&#10;com.kylas.analytics.service.csv.CsvProcessingException: Failed to process record at row 2&#10;	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessor.processCsvFile(UserUsageV2CsvProcessor.java:80)&#10;	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest.lambda$testProcessCsvFile_WithInvalidDateFormat_ShouldThrowException$2(UserUsageV2CsvProcessorTest.java:101)&#10;	at org.assertj.core.api.ThrowableAssert.catchThrowable(ThrowableAssert.java:63)&#10;	at org.assertj.core.api.AssertionsForClassTypes.catchThrowable(AssertionsForClassTypes.java:892)&#10;	at org.assertj.core.api.Assertions.catchThrowable(Assertions.java:1366)&#10;	at org.assertj.core.api.Assertions.assertThatThrownBy(Assertions.java:1210)&#10;	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest.testProcessCsvFile_WithInvalidDateFormat_ShouldThrowException(UserUsageV2CsvProcessorTest.java:101)&#10;	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)&#10;	at java.base/java.lang.reflect.Method.invoke(Method.java:580)&#10;	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:728)&#10;	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)&#10;	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)&#10;	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)&#10;	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)&#10;	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)&#10;	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)&#10;	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)&#10;	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)&#10;	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)&#10;	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)&#10;	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)&#10;	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)&#10;	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)&#10;	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:218)&#10;	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)&#10;	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:214)&#10;	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:139)&#10;	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)&#10;	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)&#10;	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)&#10;	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)&#10;	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)&#10;	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)&#10;	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)&#10;	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)&#10;	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)&#10;	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)&#10;	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)&#10;	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)&#10;	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)&#10;	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)&#10;	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)&#10;	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)&#10;	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)&#10;	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)&#10;	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)&#10;	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)&#10;	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)&#10;	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)&#10;	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)&#10;	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)&#10;	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)&#10;	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)&#10;	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)&#10;	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:55)&#10;	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:223)&#10;	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:175)&#10;	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:139)&#10;	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)&#10;	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)&#10;	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)&#10;	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)&#10;Caused by: com.kylas.analytics.service.csv.CsvProcessingException: Invalid date format for column usagePublishedDate: invalid-date&#10;	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessor.getDateTimeValue(UserUsageV2CsvProcessor.java:328)&#10;	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessor.processCoreColumns(UserUsageV2CsvProcessor.java:196)&#10;	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessor.processRow(UserUsageV2CsvProcessor.java:174)&#10;	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessor.processCsvFile(UserUsageV2CsvProcessor.java:74)&#10;	... 75 more&#10;" type="java.lang.AssertionError"><![CDATA[java.lang.AssertionError: 

Expecting throwable message:
  "Failed to process record at row 2"
to contain:
  "Invalid date format"
but did not.

Throwable that failed the check:

com.kylas.analytics.service.csv.CsvProcessingException: Failed to process record at row 2
	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessor.processCsvFile(UserUsageV2CsvProcessor.java:80)
	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest.lambda$testProcessCsvFile_WithInvalidDateFormat_ShouldThrowException$2(UserUsageV2CsvProcessorTest.java:101)
	at org.assertj.core.api.ThrowableAssert.catchThrowable(ThrowableAssert.java:63)
	at org.assertj.core.api.AssertionsForClassTypes.catchThrowable(AssertionsForClassTypes.java:892)
	at org.assertj.core.api.Assertions.catchThrowable(Assertions.java:1366)
	at org.assertj.core.api.Assertions.assertThatThrownBy(Assertions.java:1210)
	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest.testProcessCsvFile_WithInvalidDateFormat_ShouldThrowException(UserUsageV2CsvProcessorTest.java:101)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:728)
	at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
	at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
	at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
	at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
	at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:218)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:214)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:139)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:69)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:198)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:169)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:93)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:58)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:141)
	at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:57)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:103)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:85)
	at org.junit.platform.launcher.core.DelegatingLauncher.execute(DelegatingLauncher.java:47)
	at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:55)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:223)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:175)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:139)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:456)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:169)
	at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:595)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:581)
Caused by: com.kylas.analytics.service.csv.CsvProcessingException: Invalid date format for column usagePublishedDate: invalid-date
	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessor.getDateTimeValue(UserUsageV2CsvProcessor.java:328)
	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessor.processCoreColumns(UserUsageV2CsvProcessor.java:196)
	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessor.processRow(UserUsageV2CsvProcessor.java:174)
	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessor.processCsvFile(UserUsageV2CsvProcessor.java:74)
	... 75 more

	at com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest.testProcessCsvFile_WithInvalidDateFormat_ShouldThrowException(UserUsageV2CsvProcessorTest.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
]]></failure>
    <system-out><![CDATA[19:08:57.352 [main] INFO com.kylas.analytics.service.csv.UserUsageV2CsvProcessor -- Processing CSV file: /tmp/junit9841028021190302289/invalid-date.csv
19:08:57.359 [main] ERROR com.kylas.analytics.service.csv.UserUsageV2CsvProcessor -- Error processing row 2: Invalid date format for column usagePublishedDate: invalid-date
]]></system-out>
  </testcase>
  <testcase name="testValidateCsvFile_WithEmptyFile_ShouldReturnFalse" classname="com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest" time="0.004">
    <system-out><![CDATA[19:08:57.366 [main] WARN com.kylas.analytics.service.csv.UserUsageV2CsvProcessor -- CSV file is empty: /tmp/junit3082593246064922083/empty.csv
]]></system-out>
  </testcase>
  <testcase name="testValidateCsvFile_WithValidFile_ShouldReturnTrue" classname="com.kylas.analytics.service.csv.UserUsageV2CsvProcessorTest" time="0.003"/>
</testsuite>