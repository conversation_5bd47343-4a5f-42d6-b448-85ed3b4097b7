spring:
  application:
    name: customer-success-analytics-test
  
  datasource:
    url: jdbc:h2:mem:testdb
    username: sa
    password: 
    driver-class-name: org.h2.Driver

  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
    open-in-view: false

  h2:
    console:
      enabled: true

logging:
  level:
    com.kylas: DEBUG
    org.springframework.security: WARN
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE

# Application specific properties for testing
app:
  csv:
    upload-dir: ${java.io.tmpdir}/csv-uploads-test
    batch-size: 100
  analytics:
    cache-duration: 60 # seconds
    default-date-range: 7 # days
  security:
    session-timeout: 300 # seconds
