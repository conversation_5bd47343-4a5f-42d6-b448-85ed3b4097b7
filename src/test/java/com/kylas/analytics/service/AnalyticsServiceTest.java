package com.kylas.analytics.service;

import com.kylas.analytics.domain.entity.TenantOnboarding;
import com.kylas.analytics.domain.entity.UserUsage;
import com.kylas.analytics.domain.repository.TenantOnboardingRepository;
import com.kylas.analytics.domain.repository.UserUsageRepository;
import com.kylas.analytics.service.dto.DashboardMetrics;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.PageRequest;

import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Unit tests for AnalyticsService.
 * Tests business logic for analytics and reporting functionality.
 */
@ExtendWith(MockitoExtension.class)
class AnalyticsServiceTest {

    @Mock
    private TenantOnboardingRepository tenantOnboardingRepository;

    @Mock
    private UserUsageRepository userUsageRepository;

    @InjectMocks
    private AnalyticsService analyticsService;

    private TenantOnboarding testTenant;
    private UserUsage testUser;

    @BeforeEach
    void setUp() {
        testTenant = createTestTenant();
        testUser = createTestUser();
    }

    @Test
    void getDashboardMetrics_ShouldReturnCompleteMetrics() {
        // Given
        when(tenantOnboardingRepository.count()).thenReturn(10L);
        when(tenantOnboardingRepository.countByStatus("active")).thenReturn(8L);
        when(userUsageRepository.count()).thenReturn(50L);
        when(userUsageRepository.findByActive(true)).thenReturn(Arrays.asList(testUser));
        when(userUsageRepository.findByDau(true)).thenReturn(Arrays.asList(testUser));
        when(tenantOnboardingRepository.getTenantSignupTrends(any(), any())).thenReturn(new ArrayList<>());
        when(userUsageRepository.getDAUTrend(any(), any())).thenReturn(new ArrayList<>());
        when(tenantOnboardingRepository.getIndustryDistribution()).thenReturn(createIndustryData());
        when(tenantOnboardingRepository.getPlanDistribution()).thenReturn(createPlanData());
        when(tenantOnboardingRepository.getTopTenantsByActivity(any())).thenReturn(Arrays.asList(testTenant));
        when(userUsageRepository.getTopActiveUsers(any())).thenReturn(Arrays.asList(testUser));

        // When
        DashboardMetrics metrics = analyticsService.getDashboardMetrics();

        // Then
        assertThat(metrics).isNotNull();
        assertThat(metrics.getTotalTenants()).isEqualTo(10L);
        assertThat(metrics.getActiveTenants()).isEqualTo(8L);
        assertThat(metrics.getTotalUsers()).isEqualTo(50L);
        assertThat(metrics.getActiveUsers()).isEqualTo(1L);
        assertThat(metrics.getDailyActiveUsers()).isEqualTo(1L);
        assertThat(metrics.getUserEngagementRate()).isEqualTo(2.0); // 1/50 * 100
        assertThat(metrics.getTenantRetentionRate()).isEqualTo(80.0); // 8/10 * 100
    }

    @Test
    void getTenantSignupTrend_ShouldReturnTrendData() {
        // Given
        LocalDateTime startDate = LocalDateTime.now().minusDays(30);
        LocalDateTime endDate = LocalDateTime.now();
        List<Object[]> mockData = Arrays.asList(
            new Object[]{"2023-01-01", 5L},
            new Object[]{"2023-01-02", 3L}
        );
        when(tenantOnboardingRepository.getTenantSignupTrends(startDate, endDate)).thenReturn(mockData);

        // When
        List<DashboardMetrics.TrendData> result = analyticsService.getTenantSignupTrend(startDate, endDate);

        // Then
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getDate()).isEqualTo("2023-01-01");
        assertThat(result.get(0).getCount()).isEqualTo(5L);
        assertThat(result.get(1).getDate()).isEqualTo("2023-01-02");
        assertThat(result.get(1).getCount()).isEqualTo(3L);
    }

    @Test
    void getUserActivityTrend_ShouldReturnTrendData() {
        // Given
        LocalDateTime startDate = LocalDateTime.now().minusDays(30);
        LocalDateTime endDate = LocalDateTime.now();
        List<Object[]> mockData = Arrays.asList(
            new Object[]{"2023-01-01", 15L},
            new Object[]{"2023-01-02", 20L}
        );
        when(userUsageRepository.getDAUTrend(startDate, endDate)).thenReturn(mockData);

        // When
        List<DashboardMetrics.TrendData> result = analyticsService.getUserActivityTrend(startDate, endDate);

        // Then
        assertThat(result).hasSize(2);
        assertThat(result.get(0).getDate()).isEqualTo("2023-01-01");
        assertThat(result.get(0).getCount()).isEqualTo(15L);
        assertThat(result.get(1).getDate()).isEqualTo("2023-01-02");
        assertThat(result.get(1).getCount()).isEqualTo(20L);
    }

    @Test
    void getIndustryDistribution_ShouldReturnDistributionMap() {
        // Given
        List<Object[]> mockData = createIndustryData();
        when(tenantOnboardingRepository.getIndustryDistribution()).thenReturn(mockData);

        // When
        Map<String, Long> result = analyticsService.getIndustryDistribution();

        // Then
        assertThat(result).hasSize(2);
        assertThat(result.get("TECHNOLOGY")).isEqualTo(10L);
        assertThat(result.get("HEALTHCARE")).isEqualTo(5L);
    }

    @Test
    void getPlanDistribution_ShouldReturnDistributionMap() {
        // Given
        List<Object[]> mockData = createPlanData();
        when(tenantOnboardingRepository.getPlanDistribution()).thenReturn(mockData);

        // When
        Map<String, Long> result = analyticsService.getPlanDistribution();

        // Then
        assertThat(result).hasSize(2);
        assertThat(result.get("Elevate")).isEqualTo(15L);
        assertThat(result.get("Basic")).isEqualTo(8L);
    }

    @Test
    void getTopTenantsByActivity_ShouldReturnTopTenants() {
        // Given
        when(tenantOnboardingRepository.getTopTenantsByActivity(PageRequest.of(0, 10)))
            .thenReturn(Arrays.asList(testTenant));

        // When
        List<DashboardMetrics.TopTenant> result = analyticsService.getTopTenantsByActivity(10);

        // Then
        assertThat(result).hasSize(1);
        DashboardMetrics.TopTenant topTenant = result.get(0);
        assertThat(topTenant.getTenantId()).isEqualTo("TEST001");
        assertThat(topTenant.getTenantName()).isEqualTo("Test Tenant");
        assertThat(topTenant.getIndustry()).isEqualTo("TECHNOLOGY");
        assertThat(topTenant.getTotalActivity()).isGreaterThan(0);
    }

    @Test
    void getTopUsersByActivity_ShouldReturnTopUsers() {
        // Given
        when(userUsageRepository.getTopActiveUsers(PageRequest.of(0, 10)))
            .thenReturn(Arrays.asList(testUser));

        // When
        List<DashboardMetrics.TopUser> result = analyticsService.getTopUsersByActivity(10);

        // Then
        assertThat(result).hasSize(1);
        DashboardMetrics.TopUser topUser = result.get(0);
        assertThat(topUser.getUserId()).isEqualTo("USER001");
        assertThat(topUser.getFullName()).isEqualTo("Test User");
        assertThat(topUser.getEmail()).isEqualTo("<EMAIL>");
        assertThat(topUser.getTenantName()).isEqualTo("Test Tenant");
        assertThat(topUser.getTotalActivity()).isGreaterThan(0);
    }

    @Test
    void getUserEngagementMetrics_ShouldReturnEngagementData() {
        // Given
        Object[] mockData = {10L, 8L, 15L, 2.5, 3.2};
        when(userUsageRepository.getUserEngagementMetrics()).thenReturn(mockData);

        // When
        Map<String, Object> result = analyticsService.getUserEngagementMetrics();

        // Then
        assertThat(result).hasSize(5);
        assertThat(result.get("emailConnected")).isEqualTo(10L);
        assertThat(result.get("calendarConnected")).isEqualTo(8L);
        assertThat(result.get("loggedInUsers")).isEqualTo(15L);
        assertThat(result.get("avgDashboards")).isEqualTo(2.5);
        assertThat(result.get("avgAppsInstalled")).isEqualTo(3.2);
    }

    @Test
    void getMarketplaceAppUsage_ShouldReturnAppUsageStats() {
        // Given
        List<Object[]> mockData = Arrays.asList(
            new Object[]{"App1,App2", 5L},
            new Object[]{"App1,App3", 3L}
        );
        when(userUsageRepository.getMarketplaceAppUsageStats()).thenReturn(mockData);

        // When
        Map<String, Long> result = analyticsService.getMarketplaceAppUsage();

        // Then
        assertThat(result).isNotEmpty();
        assertThat(result.get("App1")).isEqualTo(8L); // 5 + 3
        assertThat(result.get("App2")).isEqualTo(5L);
        assertThat(result.get("App3")).isEqualTo(3L);
    }

    @Test
    void getUserRetentionMetrics_ShouldReturnRetentionData() {
        // Given
        Object[] mockData = {20L, 15L, 10L};
        when(userUsageRepository.getUserRetentionMetrics(any(), any())).thenReturn(mockData);

        // When
        Map<String, Long> result = analyticsService.getUserRetentionMetrics();

        // Then
        assertThat(result).hasSize(3);
        assertThat(result.get("recentlyActive")).isEqualTo(20L);
        assertThat(result.get("moderatelyActive")).isEqualTo(15L);
        assertThat(result.get("inactive")).isEqualTo(10L);
    }

    @Test
    void searchTenants_WithValidSearchTerm_ShouldReturnResults() {
        // Given
        String searchTerm = "Test";
        when(tenantOnboardingRepository.searchByTenantName(searchTerm))
            .thenReturn(Arrays.asList(testTenant));

        // When
        List<TenantOnboarding> result = analyticsService.searchTenants(searchTerm);

        // Then
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getTenantName()).isEqualTo("Test Tenant");
    }

    @Test
    void searchTenants_WithEmptySearchTerm_ShouldReturnEmptyList() {
        // When
        List<TenantOnboarding> result = analyticsService.searchTenants("");

        // Then
        assertThat(result).isEmpty();
    }

    @Test
    void searchUsers_WithValidSearchTerm_ShouldReturnResults() {
        // Given
        String searchTerm = "Test";
        when(userUsageRepository.searchUsers(searchTerm))
            .thenReturn(Arrays.asList(testUser));

        // When
        List<UserUsage> result = analyticsService.searchUsers(searchTerm);

        // Then
        assertThat(result).hasSize(1);
        assertThat(result.get(0).getFullName()).isEqualTo("Test User");
    }

    @Test
    void searchUsers_WithEmptySearchTerm_ShouldReturnEmptyList() {
        // When
        List<UserUsage> result = analyticsService.searchUsers("");

        // Then
        assertThat(result).isEmpty();
    }

    private TenantOnboarding createTestTenant() {
        TenantOnboarding tenant = new TenantOnboarding();
        tenant.setTenantId("TEST001");
        tenant.setTenantName("Test Tenant");
        tenant.setTenantIndustry("TECHNOLOGY");
        tenant.setSignedUpAt(LocalDateTime.now().minusDays(30));
        tenant.setTenantUserEmail("<EMAIL>");
        tenant.setActiveUserCount(10);
        tenant.setStatus("active");
        tenant.setLeadCount(100L);
        tenant.setDealCount(50L);
        tenant.setContactCount(200L);
        tenant.setCallCount(75L);
        tenant.setMeetingCount(25L);
        tenant.setTaskCount(150L);
        return tenant;
    }

    private UserUsage createTestUser() {
        UserUsage user = new UserUsage();
        user.setUserId("USER001");
        user.setFullName("Test User");
        user.setEmail("<EMAIL>");
        user.setTenantId("TEST001");
        user.setTenantName("Test Tenant");
        user.setUsagePublishedDate(LocalDateTime.now());
        user.setActive(true);
        user.setDau(true);
        user.setCreatedLeadCount(10);
        user.setCreatedDealCount(5);
        user.setCreatedContactCount(15);
        user.setCreatedTaskCount(20);
        user.setCreatedNoteCount(8);
        user.setCreatedMeetingCount(3);
        return user;
    }

    private List<Object[]> createIndustryData() {
        return Arrays.asList(
            new Object[]{"TECHNOLOGY", 10L},
            new Object[]{"HEALTHCARE", 5L}
        );
    }

    private List<Object[]> createPlanData() {
        return Arrays.asList(
            new Object[]{"Elevate", 15L},
            new Object[]{"Basic", 8L}
        );
    }
}
