package com.kylas.analytics.domain.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Enhanced UserUsage entity with core columns and JSONB for flexible data storage.
 * Follows Single Responsibility Principle by separating core user data from dynamic metrics.
 */
@Entity
@Table(name = "user_usage_v2", indexes = {
    @Index(name = "idx_user_usage_v2_tenant_id", columnList = "tenantId"),
    @Index(name = "idx_user_usage_v2_usage_date", columnList = "usagePublishedDate"),
    @Index(name = "idx_user_usage_v2_tenant_date", columnList = "tenantId, usagePublishedDate"),
    @Index(name = "idx_user_usage_v2_email", columnList = "email")
})
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserUsageV2 {

    @Id
    @NotBlank(message = "User ID cannot be blank")
    @Column(name = "user_id", nullable = false)
    private String userId;

    @NotBlank(message = "Full name cannot be blank")
    @Column(name = "full_name", nullable = false)
    private String fullName;

    @Email(message = "Email should be valid")
    @NotBlank(message = "Email cannot be blank")
    @Column(name = "email", nullable = false)
    private String email;

    @NotBlank(message = "Tenant ID cannot be blank")
    @Column(name = "tenant_id", nullable = false)
    private String tenantId;

    @Column(name = "tenant_name")
    private String tenantName;

    @Column(name = "plan_name")
    private String planName;

    @NotNull(message = "Usage published date cannot be null")
    @Column(name = "usage_published_date", nullable = false)
    private LocalDateTime usagePublishedDate;

    @Column(name = "primary_phone_number")
    private String primaryPhoneNumber;

    @Column(name = "all_phone_numbers")
    private String allPhoneNumbers;

    @Column(name = "status")
    private String status;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * JSONB column to store all additional metrics and dynamic data.
     * This provides flexibility for storing varying data structures without schema changes.
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "additional_data", columnDefinition = "jsonb")
    private JsonNode additionalData;

    /**
     * Audit fields for tracking record lifecycle
     */
    @Column(name = "record_created_at")
    private LocalDateTime recordCreatedAt;

    @Column(name = "record_updated_at")
    private LocalDateTime recordUpdatedAt;

    @Column(name = "version")
    private Long version = 1L;

    // Default constructor
    public UserUsageV2() {
    }

    // Constructor with core fields
    public UserUsageV2(String userId, String fullName, String email, String tenantId, 
                      LocalDateTime usagePublishedDate) {
        this.userId = userId;
        this.fullName = fullName;
        this.email = email;
        this.tenantId = tenantId;
        this.usagePublishedDate = usagePublishedDate;
    }

    @PrePersist
    protected void onCreate() {
        recordCreatedAt = LocalDateTime.now();
        recordUpdatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        recordUpdatedAt = LocalDateTime.now();
        version++;
    }

    // Getters and Setters
    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    public String getPlanName() {
        return planName;
    }

    public void setPlanName(String planName) {
        this.planName = planName;
    }

    public LocalDateTime getUsagePublishedDate() {
        return usagePublishedDate;
    }

    public void setUsagePublishedDate(LocalDateTime usagePublishedDate) {
        this.usagePublishedDate = usagePublishedDate;
    }

    public String getPrimaryPhoneNumber() {
        return primaryPhoneNumber;
    }

    public void setPrimaryPhoneNumber(String primaryPhoneNumber) {
        this.primaryPhoneNumber = primaryPhoneNumber;
    }

    public String getAllPhoneNumbers() {
        return allPhoneNumbers;
    }

    public void setAllPhoneNumbers(String allPhoneNumbers) {
        this.allPhoneNumbers = allPhoneNumbers;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public JsonNode getAdditionalData() {
        return additionalData;
    }

    public void setAdditionalData(JsonNode additionalData) {
        this.additionalData = additionalData;
    }

    public LocalDateTime getRecordCreatedAt() {
        return recordCreatedAt;
    }

    public void setRecordCreatedAt(LocalDateTime recordCreatedAt) {
        this.recordCreatedAt = recordCreatedAt;
    }

    public LocalDateTime getRecordUpdatedAt() {
        return recordUpdatedAt;
    }

    public void setRecordUpdatedAt(LocalDateTime recordUpdatedAt) {
        this.recordUpdatedAt = recordUpdatedAt;
    }

    public Long getVersion() {
        return version;
    }

    public void setVersion(Long version) {
        this.version = version;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserUsageV2 that = (UserUsageV2) o;
        return Objects.equals(userId, that.userId) && 
               Objects.equals(usagePublishedDate, that.usagePublishedDate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(userId, usagePublishedDate);
    }

    @Override
    public String toString() {
        return "UserUsageV2{" +
                "userId='" + userId + '\'' +
                ", fullName='" + fullName + '\'' +
                ", email='" + email + '\'' +
                ", tenantId='" + tenantId + '\'' +
                ", usagePublishedDate=" + usagePublishedDate +
                ", version=" + version +
                '}';
    }
}
