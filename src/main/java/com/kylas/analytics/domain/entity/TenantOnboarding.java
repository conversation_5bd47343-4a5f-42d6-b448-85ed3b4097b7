package com.kylas.analytics.domain.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Entity representing tenant onboarding data.
 * Contains information about tenant registration, configuration, and usage metrics.
 */
@Entity
@Table(name = "tenant_onboarding")
public class TenantOnboarding {

    @Id
    @Column(name = "tenant_id")
    private String tenantId;

    @NotBlank
    @Column(name = "tenant_name", nullable = false)
    private String tenantName;

    @Column(name = "tenant_industry")
    private String tenantIndustry;

    @NotNull
    @Column(name = "signed_up_at", nullable = false)
    private LocalDateTime signedUpAt;

    @Email
    @NotBlank
    @Column(name = "tenant_user_email", nullable = false)
    private String tenantUserEmail;

    @Column(name = "profile_count")
    private Integer profileCount;

    @Column(name = "account_settings_completed")
    private Boolean accountSettingsCompleted;

    @Column(name = "active_user_count")
    private Integer activeUserCount;

    @Column(name = "inactive_user_count")
    private Integer inactiveUserCount;

    @Column(name = "team_count")
    private Integer teamCount;

    @Column(name = "plan_name")
    private String planName;

    @Column(name = "status")
    private String status;

    @Column(name = "number_of_custom_dashboards_created")
    private Integer numberOfCustomDashboardsCreated;

    @Column(name = "message_count")
    private Long messageCount;

    @Column(name = "number_of_marketplace_apps_installed")
    private Integer numberOfMarketplaceAppsInstalled;

    @Column(name = "name_of_marketplace_apps_installed", length = 1000)
    private String nameOfMarketplaceAppsInstalled;

    @Column(name = "dau_true")
    private Integer dauTrue;

    @Column(name = "dau_false")
    private Integer dauFalse;

    @Column(name = "logged_in_users_count")
    private Integer loggedInUsersCount;

    @Column(name = "active_workflow_count")
    private Integer activeWorkflowCount;

    @Column(name = "active_score_rule_count")
    private Integer activeScoreRuleCount;

    @Column(name = "active_custom_fields_count")
    private Integer activeCustomFieldsCount;

    @Column(name = "inactive_custom_fields_count")
    private Integer inactiveCustomFieldsCount;

    @Column(name = "product_count")
    private Integer productCount;

    @Column(name = "import_count")
    private Integer importCount;

    @Column(name = "pipeline_count")
    private Integer pipelineCount;

    @Column(name = "lead_count")
    private Long leadCount;

    @Column(name = "deal_count")
    private Long dealCount;

    @Column(name = "contact_count")
    private Long contactCount;

    @Column(name = "company_count")
    private Long companyCount;

    @Column(name = "call_count")
    private Long callCount;

    @Column(name = "meeting_count")
    private Long meetingCount;

    @Column(name = "task_count")
    private Long taskCount;

    @Column(name = "note_count")
    private Long noteCount;

    @Column(name = "created_quote_count")
    private Long createdQuoteCount;

    @Column(name = "updated_quote_count")
    private Long updatedQuoteCount;

    @Column(name = "active_layout_count")
    private Integer activeLayoutCount;

    @Column(name = "inactive_layout_count")
    private Integer inactiveLayoutCount;

    @Column(name = "create_lead_layout_count")
    private Integer createLeadLayoutCount;

    @Column(name = "edit_lead_layout_count")
    private Integer editLeadLayoutCount;

    @Column(name = "goal_trial_active")
    private String goalTrialActive;

    @Column(name = "goal_addon")
    private String goalAddon;

    @Column(name = "number_of_goals_created")
    private Integer numberOfGoalsCreated;

    @Column(name = "subscription_id")
    private String subscriptionId;

    @Column(name = "customer_id")
    private String customerId;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    // Constructors
    public TenantOnboarding() {}

    public TenantOnboarding(String tenantId, String tenantName, LocalDateTime signedUpAt, String tenantUserEmail) {
        this.tenantId = tenantId;
        this.tenantName = tenantName;
        this.signedUpAt = signedUpAt;
        this.tenantUserEmail = tenantUserEmail;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    // Getters and Setters
    public String getTenantId() { return tenantId; }
    public void setTenantId(String tenantId) { this.tenantId = tenantId; }

    public String getTenantName() { return tenantName; }
    public void setTenantName(String tenantName) { this.tenantName = tenantName; }

    public String getTenantIndustry() { return tenantIndustry; }
    public void setTenantIndustry(String tenantIndustry) { this.tenantIndustry = tenantIndustry; }

    public LocalDateTime getSignedUpAt() { return signedUpAt; }
    public void setSignedUpAt(LocalDateTime signedUpAt) { this.signedUpAt = signedUpAt; }

    public String getTenantUserEmail() { return tenantUserEmail; }
    public void setTenantUserEmail(String tenantUserEmail) { this.tenantUserEmail = tenantUserEmail; }

    public Integer getProfileCount() { return profileCount; }
    public void setProfileCount(Integer profileCount) { this.profileCount = profileCount; }

    public Boolean getAccountSettingsCompleted() { return accountSettingsCompleted; }
    public void setAccountSettingsCompleted(Boolean accountSettingsCompleted) { this.accountSettingsCompleted = accountSettingsCompleted; }

    public Integer getActiveUserCount() { return activeUserCount; }
    public void setActiveUserCount(Integer activeUserCount) { this.activeUserCount = activeUserCount; }

    public Integer getInactiveUserCount() { return inactiveUserCount; }
    public void setInactiveUserCount(Integer inactiveUserCount) { this.inactiveUserCount = inactiveUserCount; }

    public Integer getTeamCount() { return teamCount; }
    public void setTeamCount(Integer teamCount) { this.teamCount = teamCount; }

    public String getPlanName() { return planName; }
    public void setPlanName(String planName) { this.planName = planName; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }

    public Integer getNumberOfCustomDashboardsCreated() { return numberOfCustomDashboardsCreated; }
    public void setNumberOfCustomDashboardsCreated(Integer numberOfCustomDashboardsCreated) { this.numberOfCustomDashboardsCreated = numberOfCustomDashboardsCreated; }

    public Long getMessageCount() { return messageCount; }
    public void setMessageCount(Long messageCount) { this.messageCount = messageCount; }

    public Integer getNumberOfMarketplaceAppsInstalled() { return numberOfMarketplaceAppsInstalled; }
    public void setNumberOfMarketplaceAppsInstalled(Integer numberOfMarketplaceAppsInstalled) { this.numberOfMarketplaceAppsInstalled = numberOfMarketplaceAppsInstalled; }

    public String getNameOfMarketplaceAppsInstalled() { return nameOfMarketplaceAppsInstalled; }
    public void setNameOfMarketplaceAppsInstalled(String nameOfMarketplaceAppsInstalled) { this.nameOfMarketplaceAppsInstalled = nameOfMarketplaceAppsInstalled; }

    public Integer getDauTrue() { return dauTrue; }
    public void setDauTrue(Integer dauTrue) { this.dauTrue = dauTrue; }

    public Integer getDauFalse() { return dauFalse; }
    public void setDauFalse(Integer dauFalse) { this.dauFalse = dauFalse; }

    public Integer getLoggedInUsersCount() { return loggedInUsersCount; }
    public void setLoggedInUsersCount(Integer loggedInUsersCount) { this.loggedInUsersCount = loggedInUsersCount; }

    public Integer getActiveWorkflowCount() { return activeWorkflowCount; }
    public void setActiveWorkflowCount(Integer activeWorkflowCount) { this.activeWorkflowCount = activeWorkflowCount; }

    public Integer getActiveScoreRuleCount() { return activeScoreRuleCount; }
    public void setActiveScoreRuleCount(Integer activeScoreRuleCount) { this.activeScoreRuleCount = activeScoreRuleCount; }

    public Integer getActiveCustomFieldsCount() { return activeCustomFieldsCount; }
    public void setActiveCustomFieldsCount(Integer activeCustomFieldsCount) { this.activeCustomFieldsCount = activeCustomFieldsCount; }

    public Integer getInactiveCustomFieldsCount() { return inactiveCustomFieldsCount; }
    public void setInactiveCustomFieldsCount(Integer inactiveCustomFieldsCount) { this.inactiveCustomFieldsCount = inactiveCustomFieldsCount; }

    public Integer getProductCount() { return productCount; }
    public void setProductCount(Integer productCount) { this.productCount = productCount; }

    public Integer getImportCount() { return importCount; }
    public void setImportCount(Integer importCount) { this.importCount = importCount; }

    public Integer getPipelineCount() { return pipelineCount; }
    public void setPipelineCount(Integer pipelineCount) { this.pipelineCount = pipelineCount; }

    public Long getLeadCount() { return leadCount; }
    public void setLeadCount(Long leadCount) { this.leadCount = leadCount; }

    public Long getDealCount() { return dealCount; }
    public void setDealCount(Long dealCount) { this.dealCount = dealCount; }

    public Long getContactCount() { return contactCount; }
    public void setContactCount(Long contactCount) { this.contactCount = contactCount; }

    public Long getCompanyCount() { return companyCount; }
    public void setCompanyCount(Long companyCount) { this.companyCount = companyCount; }

    public Long getCallCount() { return callCount; }
    public void setCallCount(Long callCount) { this.callCount = callCount; }

    public Long getMeetingCount() { return meetingCount; }
    public void setMeetingCount(Long meetingCount) { this.meetingCount = meetingCount; }

    public Long getTaskCount() { return taskCount; }
    public void setTaskCount(Long taskCount) { this.taskCount = taskCount; }

    public Long getNoteCount() { return noteCount; }
    public void setNoteCount(Long noteCount) { this.noteCount = noteCount; }

    public Long getCreatedQuoteCount() { return createdQuoteCount; }
    public void setCreatedQuoteCount(Long createdQuoteCount) { this.createdQuoteCount = createdQuoteCount; }

    public Long getUpdatedQuoteCount() { return updatedQuoteCount; }
    public void setUpdatedQuoteCount(Long updatedQuoteCount) { this.updatedQuoteCount = updatedQuoteCount; }

    public Integer getActiveLayoutCount() { return activeLayoutCount; }
    public void setActiveLayoutCount(Integer activeLayoutCount) { this.activeLayoutCount = activeLayoutCount; }

    public Integer getInactiveLayoutCount() { return inactiveLayoutCount; }
    public void setInactiveLayoutCount(Integer inactiveLayoutCount) { this.inactiveLayoutCount = inactiveLayoutCount; }

    public Integer getCreateLeadLayoutCount() { return createLeadLayoutCount; }
    public void setCreateLeadLayoutCount(Integer createLeadLayoutCount) { this.createLeadLayoutCount = createLeadLayoutCount; }

    public Integer getEditLeadLayoutCount() { return editLeadLayoutCount; }
    public void setEditLeadLayoutCount(Integer editLeadLayoutCount) { this.editLeadLayoutCount = editLeadLayoutCount; }

    public String getGoalTrialActive() { return goalTrialActive; }
    public void setGoalTrialActive(String goalTrialActive) { this.goalTrialActive = goalTrialActive; }

    public String getGoalAddon() { return goalAddon; }
    public void setGoalAddon(String goalAddon) { this.goalAddon = goalAddon; }

    public Integer getNumberOfGoalsCreated() { return numberOfGoalsCreated; }
    public void setNumberOfGoalsCreated(Integer numberOfGoalsCreated) { this.numberOfGoalsCreated = numberOfGoalsCreated; }

    public String getSubscriptionId() { return subscriptionId; }
    public void setSubscriptionId(String subscriptionId) { this.subscriptionId = subscriptionId; }

    public String getCustomerId() { return customerId; }
    public void setCustomerId(String customerId) { this.customerId = customerId; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TenantOnboarding that = (TenantOnboarding) o;
        return Objects.equals(tenantId, that.tenantId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(tenantId);
    }

    @Override
    public String toString() {
        return "TenantOnboarding{" +
                "tenantId='" + tenantId + '\'' +
                ", tenantName='" + tenantName + '\'' +
                ", tenantIndustry='" + tenantIndustry + '\'' +
                ", signedUpAt=" + signedUpAt +
                ", tenantUserEmail='" + tenantUserEmail + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
