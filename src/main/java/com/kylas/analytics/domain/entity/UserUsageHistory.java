package com.kylas.analytics.domain.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.JsonNode;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotNull;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Historical data entity for storing user usage snapshots.
 * Implements the Open/Closed Principle by being open for extension (new data types)
 * but closed for modification (core structure remains stable).
 */
@Entity
@Table(name = "user_usage_history", indexes = {
    @Index(name = "idx_usage_history_user_id", columnList = "userId"),
    @Index(name = "idx_usage_history_tenant_id", columnList = "tenantId"),
    @Index(name = "idx_usage_history_date", columnList = "usagePublishedDate"),
    @Index(name = "idx_usage_history_archived_at", columnList = "archivedAt"),
    @Index(name = "idx_usage_history_tenant_date", columnList = "tenantId, usagePublishedDate"),
    @Index(name = "idx_usage_history_user_date", columnList = "userId, usagePublishedDate")
})
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserUsageHistory {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @NotNull
    @Column(name = "user_id", nullable = false)
    private String userId;

    @NotNull
    @Column(name = "full_name", nullable = false)
    private String fullName;

    @NotNull
    @Column(name = "email", nullable = false)
    private String email;

    @NotNull
    @Column(name = "tenant_id", nullable = false)
    private String tenantId;

    @Column(name = "tenant_name")
    private String tenantName;

    @Column(name = "plan_name")
    private String planName;

    @NotNull
    @Column(name = "usage_published_date", nullable = false)
    private LocalDateTime usagePublishedDate;

    @Column(name = "primary_phone_number")
    private String primaryPhoneNumber;

    @Column(name = "all_phone_numbers")
    private String allPhoneNumbers;

    @Column(name = "status")
    private String status;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * Complete snapshot of additional data at the time of archiving
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "additional_data", columnDefinition = "jsonb")
    private JsonNode additionalData;

    /**
     * Metadata about the archiving process
     */
    @NotNull
    @Column(name = "archived_at", nullable = false)
    private LocalDateTime archivedAt;

    @Column(name = "original_version")
    private Long originalVersion;

    @Column(name = "archive_reason")
    private String archiveReason;

    @Column(name = "data_retention_period_days")
    private Integer dataRetentionPeriodDays;

    // Default constructor
    public UserUsageHistory() {
    }

    // Constructor from UserUsageV2
    public UserUsageHistory(UserUsageV2 userUsage, String archiveReason) {
        this.userId = userUsage.getUserId();
        this.fullName = userUsage.getFullName();
        this.email = userUsage.getEmail();
        this.tenantId = userUsage.getTenantId();
        this.tenantName = userUsage.getTenantName();
        this.planName = userUsage.getPlanName();
        this.usagePublishedDate = userUsage.getUsagePublishedDate();
        this.primaryPhoneNumber = userUsage.getPrimaryPhoneNumber();
        this.allPhoneNumbers = userUsage.getAllPhoneNumbers();
        this.status = userUsage.getStatus();
        this.createdAt = userUsage.getCreatedAt();
        this.updatedAt = userUsage.getUpdatedAt();
        this.additionalData = userUsage.getAdditionalData();
        this.originalVersion = userUsage.getVersion();
        this.archiveReason = archiveReason;
        this.archivedAt = LocalDateTime.now();
        this.dataRetentionPeriodDays = 365; // Default retention period
    }

    @PrePersist
    protected void onCreate() {
        if (archivedAt == null) {
            archivedAt = LocalDateTime.now();
        }
        if (dataRetentionPeriodDays == null) {
            dataRetentionPeriodDays = 365;
        }
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    public String getPlanName() {
        return planName;
    }

    public void setPlanName(String planName) {
        this.planName = planName;
    }

    public LocalDateTime getUsagePublishedDate() {
        return usagePublishedDate;
    }

    public void setUsagePublishedDate(LocalDateTime usagePublishedDate) {
        this.usagePublishedDate = usagePublishedDate;
    }

    public String getPrimaryPhoneNumber() {
        return primaryPhoneNumber;
    }

    public void setPrimaryPhoneNumber(String primaryPhoneNumber) {
        this.primaryPhoneNumber = primaryPhoneNumber;
    }

    public String getAllPhoneNumbers() {
        return allPhoneNumbers;
    }

    public void setAllPhoneNumbers(String allPhoneNumbers) {
        this.allPhoneNumbers = allPhoneNumbers;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public JsonNode getAdditionalData() {
        return additionalData;
    }

    public void setAdditionalData(JsonNode additionalData) {
        this.additionalData = additionalData;
    }

    public LocalDateTime getArchivedAt() {
        return archivedAt;
    }

    public void setArchivedAt(LocalDateTime archivedAt) {
        this.archivedAt = archivedAt;
    }

    public Long getOriginalVersion() {
        return originalVersion;
    }

    public void setOriginalVersion(Long originalVersion) {
        this.originalVersion = originalVersion;
    }

    public String getArchiveReason() {
        return archiveReason;
    }

    public void setArchiveReason(String archiveReason) {
        this.archiveReason = archiveReason;
    }

    public Integer getDataRetentionPeriodDays() {
        return dataRetentionPeriodDays;
    }

    public void setDataRetentionPeriodDays(Integer dataRetentionPeriodDays) {
        this.dataRetentionPeriodDays = dataRetentionPeriodDays;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserUsageHistory that = (UserUsageHistory) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "UserUsageHistory{" +
                "id=" + id +
                ", userId='" + userId + '\'' +
                ", fullName='" + fullName + '\'' +
                ", email='" + email + '\'' +
                ", tenantId='" + tenantId + '\'' +
                ", usagePublishedDate=" + usagePublishedDate +
                ", archivedAt=" + archivedAt +
                ", archiveReason='" + archiveReason + '\'' +
                '}';
    }
}
