package com.kylas.analytics.domain.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * Entity representing user usage data.
 * Contains detailed information about user activities and engagement metrics.
 */
@Entity
@Table(name = "user_usage")
public class UserUsage {

    @Id
    @Column(name = "user_id")
    private String userId;

    @NotBlank
    @Column(name = "full_name", nullable = false)
    private String fullName;

    @Email
    @NotBlank
    @Column(name = "email", nullable = false)
    private String email;

    @NotBlank
    @Column(name = "tenant_id", nullable = false)
    private String tenantId;

    @Column(name = "tenant_name")
    private String tenantName;

    @Column(name = "plan_name")
    private String planName;

    @NotNull
    @Column(name = "usage_published_date", nullable = false)
    private LocalDateTime usagePublishedDate;

    @Column(name = "primary_phone_number")
    private String primaryPhoneNumber;

    @Column(name = "all_phone_numbers")
    private String allPhoneNumbers;

    @Column(name = "status")
    private String status;

    @Column(name = "created_at")
    private LocalDateTime createdAt;

    @Column(name = "updated_at")
    private LocalDateTime updatedAt;

    @Column(name = "last_login_at")
    private LocalDateTime lastLoginAt;

    @Column(name = "deactivated_at")
    private LocalDateTime deactivatedAt;

    @Column(name = "active")
    private Boolean active;

    @Column(name = "verified")
    private Boolean verified;

    @Column(name = "created_lead_count")
    private Integer createdLeadCount;

    @Column(name = "updated_lead_count")
    private Integer updatedLeadCount;

    @Column(name = "created_deal_count")
    private Integer createdDealCount;

    @Column(name = "updated_deal_count")
    private Integer updatedDealCount;

    @Column(name = "created_contact_count")
    private Integer createdContactCount;

    @Column(name = "updated_contact_count")
    private Integer updatedContactCount;

    @Column(name = "created_task_count")
    private Integer createdTaskCount;

    @Column(name = "created_note_count")
    private Integer createdNoteCount;

    @Column(name = "created_meeting_count")
    private Integer createdMeetingCount;

    @Column(name = "created_company_count")
    private Integer createdCompanyCount;

    @Column(name = "updated_company_count")
    private Integer updatedCompanyCount;

    @Column(name = "email_account_connected")
    private Boolean emailAccountConnected;

    @Column(name = "email_connected_account_name")
    private String emailConnectedAccountName;

    @Column(name = "calendar_account_connected")
    private Boolean calendarAccountConnected;

    @Column(name = "connected_calendar_account_name")
    private String connectedCalendarAccountName;

    @Column(name = "logged_in")
    private Boolean loggedIn;

    @Column(name = "calls_logged")
    private Integer callsLogged;

    @Column(name = "emails_sent")
    private Integer emailsSent;

    @Column(name = "number_of_custom_dashboards_created")
    private Integer numberOfCustomDashboardsCreated;

    @Column(name = "message_count")
    private Long messageCount;

    @Column(name = "number_of_marketplace_apps_installed")
    private Integer numberOfMarketplaceAppsInstalled;

    @Column(name = "name_of_marketplace_apps_installed", length = 1000)
    private String nameOfMarketplaceAppsInstalled;

    @Column(name = "created_quote_count")
    private Integer createdQuoteCount;

    @Column(name = "updated_quote_count")
    private Integer updatedQuoteCount;

    @Column(name = "dau")
    private Boolean dau;

    @Column(name = "record_created_at")
    private LocalDateTime recordCreatedAt;

    @Column(name = "record_updated_at")
    private LocalDateTime recordUpdatedAt;

    // Constructors
    public UserUsage() {}

    public UserUsage(String userId, String fullName, String email, String tenantId, LocalDateTime usagePublishedDate) {
        this.userId = userId;
        this.fullName = fullName;
        this.email = email;
        this.tenantId = tenantId;
        this.usagePublishedDate = usagePublishedDate;
        this.recordCreatedAt = LocalDateTime.now();
        this.recordUpdatedAt = LocalDateTime.now();
    }

    @PrePersist
    protected void onCreate() {
        recordCreatedAt = LocalDateTime.now();
        recordUpdatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        recordUpdatedAt = LocalDateTime.now();
    }

    // Getters and Setters
    public String getUserId() { return userId; }
    public void setUserId(String userId) { this.userId = userId; }

    public String getFullName() { return fullName; }
    public void setFullName(String fullName) { this.fullName = fullName; }

    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }

    public String getTenantId() { return tenantId; }
    public void setTenantId(String tenantId) { this.tenantId = tenantId; }

    public String getTenantName() { return tenantName; }
    public void setTenantName(String tenantName) { this.tenantName = tenantName; }

    public String getPlanName() { return planName; }
    public void setPlanName(String planName) { this.planName = planName; }

    public LocalDateTime getUsagePublishedDate() { return usagePublishedDate; }
    public void setUsagePublishedDate(LocalDateTime usagePublishedDate) { this.usagePublishedDate = usagePublishedDate; }

    public String getPrimaryPhoneNumber() { return primaryPhoneNumber; }
    public void setPrimaryPhoneNumber(String primaryPhoneNumber) { this.primaryPhoneNumber = primaryPhoneNumber; }

    public String getAllPhoneNumbers() { return allPhoneNumbers; }
    public void setAllPhoneNumbers(String allPhoneNumbers) { this.allPhoneNumbers = allPhoneNumbers; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }

    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }

    public LocalDateTime getLastLoginAt() { return lastLoginAt; }
    public void setLastLoginAt(LocalDateTime lastLoginAt) { this.lastLoginAt = lastLoginAt; }

    public LocalDateTime getDeactivatedAt() { return deactivatedAt; }
    public void setDeactivatedAt(LocalDateTime deactivatedAt) { this.deactivatedAt = deactivatedAt; }

    public Boolean getActive() { return active; }
    public void setActive(Boolean active) { this.active = active; }

    public Boolean getVerified() { return verified; }
    public void setVerified(Boolean verified) { this.verified = verified; }

    public Integer getCreatedLeadCount() { return createdLeadCount; }
    public void setCreatedLeadCount(Integer createdLeadCount) { this.createdLeadCount = createdLeadCount; }

    public Integer getUpdatedLeadCount() { return updatedLeadCount; }
    public void setUpdatedLeadCount(Integer updatedLeadCount) { this.updatedLeadCount = updatedLeadCount; }

    public Integer getCreatedDealCount() { return createdDealCount; }
    public void setCreatedDealCount(Integer createdDealCount) { this.createdDealCount = createdDealCount; }

    public Integer getUpdatedDealCount() { return updatedDealCount; }
    public void setUpdatedDealCount(Integer updatedDealCount) { this.updatedDealCount = updatedDealCount; }

    public Integer getCreatedContactCount() { return createdContactCount; }
    public void setCreatedContactCount(Integer createdContactCount) { this.createdContactCount = createdContactCount; }

    public Integer getUpdatedContactCount() { return updatedContactCount; }
    public void setUpdatedContactCount(Integer updatedContactCount) { this.updatedContactCount = updatedContactCount; }

    public Integer getCreatedTaskCount() { return createdTaskCount; }
    public void setCreatedTaskCount(Integer createdTaskCount) { this.createdTaskCount = createdTaskCount; }

    public Integer getCreatedNoteCount() { return createdNoteCount; }
    public void setCreatedNoteCount(Integer createdNoteCount) { this.createdNoteCount = createdNoteCount; }

    public Integer getCreatedMeetingCount() { return createdMeetingCount; }
    public void setCreatedMeetingCount(Integer createdMeetingCount) { this.createdMeetingCount = createdMeetingCount; }

    public Integer getCreatedCompanyCount() { return createdCompanyCount; }
    public void setCreatedCompanyCount(Integer createdCompanyCount) { this.createdCompanyCount = createdCompanyCount; }

    public Integer getUpdatedCompanyCount() { return updatedCompanyCount; }
    public void setUpdatedCompanyCount(Integer updatedCompanyCount) { this.updatedCompanyCount = updatedCompanyCount; }

    public Boolean getEmailAccountConnected() { return emailAccountConnected; }
    public void setEmailAccountConnected(Boolean emailAccountConnected) { this.emailAccountConnected = emailAccountConnected; }

    public String getEmailConnectedAccountName() { return emailConnectedAccountName; }
    public void setEmailConnectedAccountName(String emailConnectedAccountName) { this.emailConnectedAccountName = emailConnectedAccountName; }

    public Boolean getCalendarAccountConnected() { return calendarAccountConnected; }
    public void setCalendarAccountConnected(Boolean calendarAccountConnected) { this.calendarAccountConnected = calendarAccountConnected; }

    public String getConnectedCalendarAccountName() { return connectedCalendarAccountName; }
    public void setConnectedCalendarAccountName(String connectedCalendarAccountName) { this.connectedCalendarAccountName = connectedCalendarAccountName; }

    public Boolean getLoggedIn() { return loggedIn; }
    public void setLoggedIn(Boolean loggedIn) { this.loggedIn = loggedIn; }

    public Integer getCallsLogged() { return callsLogged; }
    public void setCallsLogged(Integer callsLogged) { this.callsLogged = callsLogged; }

    public Integer getEmailsSent() { return emailsSent; }
    public void setEmailsSent(Integer emailsSent) { this.emailsSent = emailsSent; }

    public Integer getNumberOfCustomDashboardsCreated() { return numberOfCustomDashboardsCreated; }
    public void setNumberOfCustomDashboardsCreated(Integer numberOfCustomDashboardsCreated) { this.numberOfCustomDashboardsCreated = numberOfCustomDashboardsCreated; }

    public Long getMessageCount() { return messageCount; }
    public void setMessageCount(Long messageCount) { this.messageCount = messageCount; }

    public Integer getNumberOfMarketplaceAppsInstalled() { return numberOfMarketplaceAppsInstalled; }
    public void setNumberOfMarketplaceAppsInstalled(Integer numberOfMarketplaceAppsInstalled) { this.numberOfMarketplaceAppsInstalled = numberOfMarketplaceAppsInstalled; }

    public String getNameOfMarketplaceAppsInstalled() { return nameOfMarketplaceAppsInstalled; }
    public void setNameOfMarketplaceAppsInstalled(String nameOfMarketplaceAppsInstalled) { this.nameOfMarketplaceAppsInstalled = nameOfMarketplaceAppsInstalled; }

    public Integer getCreatedQuoteCount() { return createdQuoteCount; }
    public void setCreatedQuoteCount(Integer createdQuoteCount) { this.createdQuoteCount = createdQuoteCount; }

    public Integer getUpdatedQuoteCount() { return updatedQuoteCount; }
    public void setUpdatedQuoteCount(Integer updatedQuoteCount) { this.updatedQuoteCount = updatedQuoteCount; }

    public Boolean getDau() { return dau; }
    public void setDau(Boolean dau) { this.dau = dau; }

    public LocalDateTime getRecordCreatedAt() { return recordCreatedAt; }
    public void setRecordCreatedAt(LocalDateTime recordCreatedAt) { this.recordCreatedAt = recordCreatedAt; }

    public LocalDateTime getRecordUpdatedAt() { return recordUpdatedAt; }
    public void setRecordUpdatedAt(LocalDateTime recordUpdatedAt) { this.recordUpdatedAt = recordUpdatedAt; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        UserUsage userUsage = (UserUsage) o;
        return Objects.equals(userId, userUsage.userId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(userId);
    }

    @Override
    public String toString() {
        return "UserUsage{" +
                "userId='" + userId + '\'' +
                ", fullName='" + fullName + '\'' +
                ", email='" + email + '\'' +
                ", tenantId='" + tenantId + '\'' +
                ", tenantName='" + tenantName + '\'' +
                ", usagePublishedDate=" + usagePublishedDate +
                ", status='" + status + '\'' +
                '}';
    }
}
