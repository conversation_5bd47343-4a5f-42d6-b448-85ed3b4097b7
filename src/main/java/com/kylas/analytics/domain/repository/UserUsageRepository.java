package com.kylas.analytics.domain.repository;

import com.kylas.analytics.domain.entity.UserUsage;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository interface for UserUsage entity.
 * Provides data access methods for user usage analytics.
 */
@Repository
public interface UserUsageRepository extends JpaRepository<UserUsage, String> {

    /**
     * Find users by tenant ID
     */
    List<UserUsage> findByTenantId(String tenantId);

    /**
     * Find users by status
     */
    List<UserUsage> findByStatus(String status);

    /**
     * Find active users
     */
    List<UserUsage> findByActive(Boolean active);

    /**
     * Find users by DAU status
     */
    List<UserUsage> findByDau(Boolean dau);

    /**
     * Find users by email
     */
    Optional<UserUsage> findByEmail(String email);

    /**
     * Find users by tenant ID with pagination
     */
    Page<UserUsage> findByTenantId(String tenantId, Pageable pageable);

    /**
     * Find users by usage date range
     */
    List<UserUsage> findByUsagePublishedDateBetween(LocalDateTime startDate, LocalDateTime endDate);

    /**
     * Count active users by tenant
     */
    @Query("SELECT COUNT(u) FROM UserUsage u WHERE u.tenantId = :tenantId AND u.active = true")
    Long countActiveUsersByTenant(@Param("tenantId") String tenantId);

    /**
     * Count DAU users by tenant
     */
    @Query("SELECT COUNT(u) FROM UserUsage u WHERE u.tenantId = :tenantId AND u.dau = true")
    Long countDAUByTenant(@Param("tenantId") String tenantId);

    /**
     * Get daily active users trend
     */
    @Query("SELECT DATE(u.usagePublishedDate) as usageDate, COUNT(u) as dauCount " +
           "FROM UserUsage u " +
           "WHERE u.dau = true AND u.usagePublishedDate BETWEEN :startDate AND :endDate " +
           "GROUP BY DATE(u.usagePublishedDate) " +
           "ORDER BY usageDate")
    List<Object[]> getDAUTrend(@Param("startDate") LocalDateTime startDate, 
                               @Param("endDate") LocalDateTime endDate);

    /**
     * Get user activity metrics by tenant
     */
    @Query("SELECT u.tenantId, u.tenantName, " +
           "COUNT(u) as totalUsers, " +
           "COUNT(CASE WHEN u.active = true THEN 1 END) as activeUsers, " +
           "COUNT(CASE WHEN u.dau = true THEN 1 END) as dauUsers, " +
           "AVG(u.createdLeadCount) as avgLeadsCreated, " +
           "AVG(u.createdDealCount) as avgDealsCreated " +
           "FROM UserUsage u " +
           "GROUP BY u.tenantId, u.tenantName " +
           "ORDER BY dauUsers DESC")
    List<Object[]> getUserActivityMetricsByTenant();

    /**
     * Get top active users by activity
     */
    @Query("SELECT u FROM UserUsage u " +
           "WHERE u.active = true " +
           "ORDER BY (u.createdLeadCount + u.createdDealCount + u.createdContactCount + u.createdTaskCount) DESC")
    List<UserUsage> getTopActiveUsers(Pageable pageable);

    /**
     * Get user engagement metrics
     */
    @Query("SELECT " +
           "COUNT(CASE WHEN u.emailAccountConnected = true THEN 1 END) as emailConnected, " +
           "COUNT(CASE WHEN u.calendarAccountConnected = true THEN 1 END) as calendarConnected, " +
           "COUNT(CASE WHEN u.loggedIn = true THEN 1 END) as loggedInUsers, " +
           "AVG(u.numberOfCustomDashboardsCreated) as avgDashboards, " +
           "AVG(u.numberOfMarketplaceAppsInstalled) as avgAppsInstalled " +
           "FROM UserUsage u " +
           "WHERE u.active = true")
    Object[] getUserEngagementMetrics();

    /**
     * Get plan-wise user distribution
     */
    @Query("SELECT u.planName, COUNT(u) as userCount " +
           "FROM UserUsage u " +
           "WHERE u.planName IS NOT NULL " +
           "GROUP BY u.planName " +
           "ORDER BY userCount DESC")
    List<Object[]> getPlanWiseUserDistribution();

    /**
     * Get user activity by time period
     */
    @Query("SELECT HOUR(u.lastLoginAt) as hour, COUNT(u) as loginCount " +
           "FROM UserUsage u " +
           "WHERE u.lastLoginAt IS NOT NULL " +
           "AND u.lastLoginAt BETWEEN :startDate AND :endDate " +
           "GROUP BY HOUR(u.lastLoginAt) " +
           "ORDER BY hour")
    List<Object[]> getUserActivityByHour(@Param("startDate") LocalDateTime startDate, 
                                        @Param("endDate") LocalDateTime endDate);

    /**
     * Get marketplace app usage statistics
     */
    @Query("SELECT u.nameOfMarketplaceAppsInstalled, COUNT(u) as usageCount " +
           "FROM UserUsage u " +
           "WHERE u.nameOfMarketplaceAppsInstalled IS NOT NULL " +
           "AND u.nameOfMarketplaceAppsInstalled != '' " +
           "GROUP BY u.nameOfMarketplaceAppsInstalled " +
           "ORDER BY usageCount DESC")
    List<Object[]> getMarketplaceAppUsageStats();

    /**
     * Find users with high activity (above threshold)
     */
    @Query("SELECT u FROM UserUsage u " +
           "WHERE (u.createdLeadCount + u.createdDealCount + u.createdContactCount) > :threshold " +
           "ORDER BY (u.createdLeadCount + u.createdDealCount + u.createdContactCount) DESC")
    List<UserUsage> findHighActivityUsers(@Param("threshold") Integer threshold);

    /**
     * Get user retention metrics
     */
    @Query("SELECT " +
           "COUNT(CASE WHEN u.lastLoginAt >= :recentDate THEN 1 END) as recentlyActive, " +
           "COUNT(CASE WHEN u.lastLoginAt < :recentDate AND u.lastLoginAt >= :oldDate THEN 1 END) as moderatelyActive, " +
           "COUNT(CASE WHEN u.lastLoginAt < :oldDate THEN 1 END) as inactive " +
           "FROM UserUsage u " +
           "WHERE u.lastLoginAt IS NOT NULL")
    Object[] getUserRetentionMetrics(@Param("recentDate") LocalDateTime recentDate, 
                                    @Param("oldDate") LocalDateTime oldDate);

    /**
     * Search users by name or email
     */
    @Query("SELECT u FROM UserUsage u " +
           "WHERE LOWER(u.fullName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) " +
           "OR LOWER(u.email) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    List<UserUsage> searchUsers(@Param("searchTerm") String searchTerm);

    /**
     * Get user creation trends
     */
    @Query("SELECT DATE(u.createdAt) as creationDate, COUNT(u) as userCount " +
           "FROM UserUsage u " +
           "WHERE u.createdAt BETWEEN :startDate AND :endDate " +
           "GROUP BY DATE(u.createdAt) " +
           "ORDER BY creationDate")
    List<Object[]> getUserCreationTrends(@Param("startDate") LocalDateTime startDate, 
                                        @Param("endDate") LocalDateTime endDate);
}
