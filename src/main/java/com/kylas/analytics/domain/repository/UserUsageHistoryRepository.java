package com.kylas.analytics.domain.repository;

import com.kylas.analytics.domain.entity.UserUsageHistory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Repository for UserUsageHistory entity.
 * Optimized for analytical queries and historical data reporting.
 */
@Repository
public interface UserUsageHistoryRepository extends JpaRepository<UserUsageHistory, Long> {

    /**
     * Find historical records by user ID
     */
    List<UserUsageHistory> findByUserIdOrderByUsagePublishedDateDesc(String userId);

    /**
     * Find historical records by tenant ID
     */
    List<UserUsageHistory> findByTenantIdOrderByUsagePublishedDateDesc(String tenantId);

    /**
     * Find historical records within date range
     */
    @Query("SELECT h FROM UserUsageHistory h WHERE h.usagePublishedDate BETWEEN :startDate AND :endDate " +
           "ORDER BY h.usagePublishedDate DESC")
    List<UserUsageHistory> findByUsagePublishedDateBetween(
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate
    );

    /**
     * Find historical records by tenant and date range
     */
    @Query("SELECT h FROM UserUsageHistory h WHERE h.tenantId = :tenantId " +
           "AND h.usagePublishedDate BETWEEN :startDate AND :endDate " +
           "ORDER BY h.usagePublishedDate DESC")
    List<UserUsageHistory> findByTenantIdAndUsagePublishedDateBetween(
        @Param("tenantId") String tenantId,
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate
    );

    // Temporarily commented out for H2 compatibility
    /*
    @Query("SELECT CAST(h.usagePublishedDate AS DATE) as usageDate, " +
           "COUNT(DISTINCT h.userId) as dailyActiveUsers " +
           "FROM UserUsageHistory h " +
           "WHERE h.usagePublishedDate BETWEEN :startDate AND :endDate " +
           "GROUP BY CAST(h.usagePublishedDate AS DATE) " +
           "ORDER BY usageDate")
    List<Object[]> getDAUTrend(
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate
    );

    @Query("SELECT " +
           "COUNT(DISTINCT h.userId) as emailConnected, " +
           "COUNT(DISTINCT h.userId) as calendarConnected, " +
           "COUNT(DISTINCT h.userId) as loggedInUsers, " +
           "AVG(1.0) as avgDashboards, " +
           "AVG(1.0) as avgAppsInstalled " +
           "FROM UserUsageHistory h " +
           "WHERE h.usagePublishedDate BETWEEN :startDate AND :endDate")
    Object[] getUserEngagementMetrics(
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate
    );

    @Query("SELECT h.userId, h.fullName, h.email, h.tenantName, " +
           "COUNT(*) as totalActivity, " +
           "1 as isDAU " +
           "FROM UserUsageHistory h " +
           "WHERE h.usagePublishedDate BETWEEN :startDate AND :endDate " +
           "GROUP BY h.userId, h.fullName, h.email, h.tenantName " +
           "ORDER BY totalActivity DESC")
    List<Object[]> getTopActiveUsers(
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate,
        Pageable pageable
    );

    @Query("SELECT h.tenantId, h.tenantName, " +
           "'marketplace-apps' as apps, " +
           "COUNT(*) as usageCount " +
           "FROM UserUsageHistory h " +
           "WHERE h.usagePublishedDate BETWEEN :startDate AND :endDate " +
           "GROUP BY h.tenantId, h.tenantName")
    List<Object[]> getMarketplaceAppUsageStats(
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate
    );
    */

    /**
     * Get user retention metrics
     */
    @Query("SELECT " +
           "COUNT(DISTINCT CASE WHEN h.usagePublishedDate >= :recentDate THEN h.userId END) as recentlyActive, " +
           "COUNT(DISTINCT CASE WHEN h.usagePublishedDate BETWEEN :moderateDate AND :recentDate THEN h.userId END) as moderatelyActive, " +
           "COUNT(DISTINCT CASE WHEN h.usagePublishedDate < :moderateDate THEN h.userId END) as inactive " +
           "FROM UserUsageHistory h")
    Object[] getUserRetentionMetrics(
        @Param("recentDate") LocalDateTime recentDate,
        @Param("moderateDate") LocalDateTime moderateDate
    );

    /**
     * Search historical records
     */
    @Query("SELECT h FROM UserUsageHistory h WHERE " +
           "LOWER(h.fullName) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(h.email) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(h.tenantName) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    List<UserUsageHistory> searchHistoricalRecords(@Param("searchTerm") String searchTerm);

    /**
     * Get records archived within date range
     */
    @Query("SELECT h FROM UserUsageHistory h WHERE h.archivedAt BETWEEN :startDate AND :endDate")
    List<UserUsageHistory> findByArchivedAtBetween(
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate
    );

    /**
     * Get records by archive reason
     */
    List<UserUsageHistory> findByArchiveReason(String archiveReason);

    /**
     * Count records by tenant and date range
     */
    @Query("SELECT COUNT(*) FROM UserUsageHistory h WHERE h.tenantId = :tenantId " +
           "AND h.usagePublishedDate BETWEEN :startDate AND :endDate")
    long countByTenantIdAndUsagePublishedDateBetween(
        @Param("tenantId") String tenantId,
        @Param("startDate") LocalDateTime startDate, 
        @Param("endDate") LocalDateTime endDate
    );

    // Temporarily commented out for H2 compatibility
    /*
    @Query("SELECT h.tenantId, h.tenantName, CAST(h.usagePublishedDate AS DATE) as usageDate, " +
           "COUNT(*) as recordCount, COUNT(DISTINCT h.userId) as uniqueUsers " +
           "FROM UserUsageHistory h " +
           "WHERE h.usagePublishedDate BETWEEN :startDate AND :endDate " +
           "GROUP BY h.tenantId, h.tenantName, CAST(h.usagePublishedDate AS DATE) " +
           "ORDER BY h.tenantId, usageDate")
    List<Object[]> getUsageTrendsByTenant(
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate
    );
    */

    /**
     * Delete records older than retention period
     */
    @Modifying
    @Query("DELETE FROM UserUsageHistory h WHERE h.archivedAt < :cutoffDate")
    int deleteRecordsOlderThan(@Param("cutoffDate") LocalDateTime cutoffDate);

    // Temporarily commented out for H2 compatibility
    /*
    @Query("SELECT CAST(h.archivedAt AS DATE) as archiveDate, COUNT(*) as recordCount, " +
           "COUNT(DISTINCT h.userId) as uniqueUsers, COUNT(DISTINCT h.tenantId) as uniqueTenants " +
           "FROM UserUsageHistory h " +
           "WHERE h.archivedAt BETWEEN :startDate AND :endDate " +
           "GROUP BY CAST(h.archivedAt AS DATE) ORDER BY archiveDate")
    List<Object[]> getDataVolumeStatistics(
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate
    );

    @Query("SELECT h FROM UserUsageHistory h WHERE h.additionalData IS NOT NULL")
    List<UserUsageHistory> findByJsonCriteria(
        @Param("jsonPath") String jsonPath,
        @Param("value") String value
    );

    @Query("SELECT " +
           "CAST(h.usagePublishedDate AS DATE) as period, " +
           "COUNT(*) as totalRecords, " +
           "COUNT(DISTINCT h.userId) as uniqueUsers, " +
           "COUNT(DISTINCT h.tenantId) as uniqueTenants, " +
           "AVG(1.0) as avgLeads, " +
           "AVG(1.0) as avgDeals " +
           "FROM UserUsageHistory h " +
           "WHERE h.usagePublishedDate BETWEEN :startDate AND :endDate " +
           "GROUP BY CAST(h.usagePublishedDate AS DATE) " +
           "ORDER BY period")
    List<Object[]> getAggregatedMetricsByPeriod(
        @Param("startDate") LocalDateTime startDate,
        @Param("endDate") LocalDateTime endDate
    );
    */
}
