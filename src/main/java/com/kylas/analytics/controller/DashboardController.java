package com.kylas.analytics.controller;

import com.kylas.analytics.service.AnalyticsService;
import com.kylas.analytics.service.dto.DashboardMetrics;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Map;

/**
 * Controller for dashboard and main analytics views.
 * Handles web requests for the main dashboard interface.
 */
@Controller
public class DashboardController {

    private static final Logger logger = LoggerFactory.getLogger(DashboardController.class);

    private final AnalyticsService analyticsService;

    @Autowired
    public DashboardController(AnalyticsService analyticsService) {
        this.analyticsService = analyticsService;
    }

    /**
     * Main dashboard page
     */
    @GetMapping({"/", "/dashboard"})
    public String dashboard(Model model) {
        logger.info("Loading dashboard");
        
        try {
            DashboardMetrics metrics = analyticsService.getDashboardMetrics();
            model.addAttribute("metrics", metrics);
            
            // Additional metrics for dashboard widgets
            Map<String, Object> engagementMetrics = analyticsService.getUserEngagementMetrics();
            model.addAttribute("engagementMetrics", engagementMetrics);
            
            Map<String, Long> appUsage = analyticsService.getMarketplaceAppUsage();
            model.addAttribute("appUsage", appUsage);
            
            Map<String, Long> retentionMetrics = analyticsService.getUserRetentionMetrics();
            model.addAttribute("retentionMetrics", retentionMetrics);
            
            logger.info("Dashboard loaded successfully");
            return "dashboard";
            
        } catch (Exception e) {
            logger.error("Error loading dashboard", e);
            model.addAttribute("error", "Failed to load dashboard metrics: " + e.getMessage());
            return "error";
        }
    }

    /**
     * Tenants analytics page
     */
    @GetMapping("/tenants")
    public String tenants(Model model, @RequestParam(required = false) String search) {
        logger.info("Loading tenants page with search: {}", search);
        
        try {
            if (search != null && !search.trim().isEmpty()) {
                model.addAttribute("tenants", analyticsService.searchTenants(search));
                model.addAttribute("searchTerm", search);
            } else {
                model.addAttribute("topTenants", analyticsService.getTopTenantsByActivity(50));
            }
            
            model.addAttribute("industryDistribution", analyticsService.getIndustryDistribution());
            model.addAttribute("planDistribution", analyticsService.getPlanDistribution());
            
            return "tenants";
            
        } catch (Exception e) {
            logger.error("Error loading tenants page", e);
            model.addAttribute("error", "Failed to load tenants data: " + e.getMessage());
            return "error";
        }
    }

    /**
     * Users analytics page
     */
    @GetMapping("/users")
    public String users(Model model, @RequestParam(required = false) String search) {
        logger.info("Loading users page with search: {}", search);
        
        try {
            if (search != null && !search.trim().isEmpty()) {
                model.addAttribute("users", analyticsService.searchUsers(search));
                model.addAttribute("searchTerm", search);
            } else {
                model.addAttribute("topUsers", analyticsService.getTopUsersByActivity(50));
            }
            
            model.addAttribute("engagementMetrics", analyticsService.getUserEngagementMetrics());
            model.addAttribute("retentionMetrics", analyticsService.getUserRetentionMetrics());
            
            return "users";
            
        } catch (Exception e) {
            logger.error("Error loading users page", e);
            model.addAttribute("error", "Failed to load users data: " + e.getMessage());
            return "error";
        }
    }

    /**
     * Analytics insights page
     */
    @GetMapping("/insights")
    public String insights(Model model) {
        logger.info("Loading insights page");
        
        try {
            DashboardMetrics metrics = analyticsService.getDashboardMetrics();
            model.addAttribute("metrics", metrics);
            
            model.addAttribute("appUsage", analyticsService.getMarketplaceAppUsage());
            model.addAttribute("engagementMetrics", analyticsService.getUserEngagementMetrics());
            
            return "insights";
            
        } catch (Exception e) {
            logger.error("Error loading insights page", e);
            model.addAttribute("error", "Failed to load insights: " + e.getMessage());
            return "error";
        }
    }

    /**
     * Login page
     */
    @GetMapping("/login")
    public String login(@RequestParam(required = false) String error,
                       @RequestParam(required = false) String logout,
                       Model model) {
        
        if (error != null) {
            model.addAttribute("error", "Invalid username or password");
        }
        
        if (logout != null) {
            model.addAttribute("message", "You have been logged out successfully");
        }
        
        return "login";
    }

    /**
     * Error page
     */
    @GetMapping("/error")
    public String error(Model model) {
        model.addAttribute("error", "An unexpected error occurred");
        return "error";
    }
}
