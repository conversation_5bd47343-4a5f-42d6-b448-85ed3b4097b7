package com.kylas.analytics.controller;

import com.kylas.analytics.service.CsvDataLoadingService;
import com.kylas.analytics.service.csv.CsvProcessingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Controller for data management operations.
 * Handles CSV file uploads and data loading operations.
 */
@Controller
@RequestMapping("/data")
public class DataManagementController {

    private static final Logger logger = LoggerFactory.getLogger(DataManagementController.class);

    private final CsvDataLoadingService csvDataLoadingService;

    @Autowired
    public DataManagementController(CsvDataLoadingService csvDataLoadingService) {
        this.csvDataLoadingService = csvDataLoadingService;
    }

    /**
     * Data management page
     */
    @GetMapping("/manage")
    public String dataManagement(Model model) {
        logger.info("Loading data management page");
        
        try {
            long[] counts = csvDataLoadingService.getDataCounts();
            model.addAttribute("tenantCount", counts[0]);
            model.addAttribute("userCount", counts[1]);
            
            return "data-management";
            
        } catch (Exception e) {
            logger.error("Error loading data management page", e);
            model.addAttribute("error", "Failed to load data counts: " + e.getMessage());
            return "error";
        }
    }

    /**
     * Load initial CSV data from workspace
     */
    @PostMapping("/load-initial")
    public String loadInitialData(RedirectAttributes redirectAttributes) {
        logger.info("Loading initial CSV data from workspace");
        
        try {
            // Load data from the CSV files in the workspace
            String tenantCsvPath = "onboarding-test.csv";
            String userCsvPath = "usage-test.csv";
            
            // Check if files exist
            if (!Files.exists(Paths.get(tenantCsvPath))) {
                redirectAttributes.addFlashAttribute("error", "Tenant onboarding CSV file not found: " + tenantCsvPath);
                return "redirect:/data/manage";
            }
            
            if (!Files.exists(Paths.get(userCsvPath))) {
                redirectAttributes.addFlashAttribute("error", "User usage CSV file not found: " + userCsvPath);
                return "redirect:/data/manage";
            }
            
            int totalRecords = csvDataLoadingService.loadAllData(tenantCsvPath, userCsvPath);
            
            redirectAttributes.addFlashAttribute("success", 
                String.format("Successfully loaded %d records from CSV files", totalRecords));
            
            logger.info("Initial data loaded successfully: {} records", totalRecords);
            
        } catch (CsvProcessingException e) {
            logger.error("Error loading initial CSV data", e);
            redirectAttributes.addFlashAttribute("error", "Failed to load CSV data: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error loading initial data", e);
            redirectAttributes.addFlashAttribute("error", "Unexpected error: " + e.getMessage());
        }
        
        return "redirect:/data/manage";
    }

    /**
     * Upload and load tenant onboarding CSV
     */
    @PostMapping("/upload-tenants")
    public String uploadTenantData(@RequestParam("file") MultipartFile file,
                                  RedirectAttributes redirectAttributes) {
        logger.info("Uploading tenant CSV file: {}", file.getOriginalFilename());
        
        if (file.isEmpty()) {
            redirectAttributes.addFlashAttribute("error", "Please select a file to upload");
            return "redirect:/data/manage";
        }
        
        try {
            // Save uploaded file temporarily
            String fileName = "tenant_" + System.currentTimeMillis() + ".csv";
            Path tempFile = Files.createTempFile("tenant_upload_", ".csv");
            file.transferTo(tempFile.toFile());
            
            // Load data from uploaded file
            int recordsLoaded = csvDataLoadingService.loadTenantOnboardingData(tempFile.toString());
            
            // Clean up temp file
            Files.deleteIfExists(tempFile);
            
            redirectAttributes.addFlashAttribute("success", 
                String.format("Successfully loaded %d tenant records", recordsLoaded));
            
            logger.info("Tenant CSV uploaded and loaded successfully: {} records", recordsLoaded);
            
        } catch (CsvProcessingException e) {
            logger.error("Error processing tenant CSV upload", e);
            redirectAttributes.addFlashAttribute("error", "Failed to process CSV: " + e.getMessage());
        } catch (IOException e) {
            logger.error("Error handling file upload", e);
            redirectAttributes.addFlashAttribute("error", "File upload error: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error during tenant CSV upload", e);
            redirectAttributes.addFlashAttribute("error", "Unexpected error: " + e.getMessage());
        }
        
        return "redirect:/data/manage";
    }

    /**
     * Upload and load user usage CSV
     */
    @PostMapping("/upload-users")
    public String uploadUserData(@RequestParam("file") MultipartFile file,
                                RedirectAttributes redirectAttributes) {
        logger.info("Uploading user CSV file: {}", file.getOriginalFilename());
        
        if (file.isEmpty()) {
            redirectAttributes.addFlashAttribute("error", "Please select a file to upload");
            return "redirect:/data/manage";
        }
        
        try {
            // Save uploaded file temporarily
            String fileName = "user_" + System.currentTimeMillis() + ".csv";
            Path tempFile = Files.createTempFile("user_upload_", ".csv");
            file.transferTo(tempFile.toFile());
            
            // Load data from uploaded file
            int recordsLoaded = csvDataLoadingService.loadUserUsageData(tempFile.toString());
            
            // Clean up temp file
            Files.deleteIfExists(tempFile);
            
            redirectAttributes.addFlashAttribute("success", 
                String.format("Successfully loaded %d user records", recordsLoaded));
            
            logger.info("User CSV uploaded and loaded successfully: {} records", recordsLoaded);
            
        } catch (CsvProcessingException e) {
            logger.error("Error processing user CSV upload", e);
            redirectAttributes.addFlashAttribute("error", "Failed to process CSV: " + e.getMessage());
        } catch (IOException e) {
            logger.error("Error handling file upload", e);
            redirectAttributes.addFlashAttribute("error", "File upload error: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Unexpected error during user CSV upload", e);
            redirectAttributes.addFlashAttribute("error", "Unexpected error: " + e.getMessage());
        }
        
        return "redirect:/data/manage";
    }

    /**
     * Clear all data
     */
    @PostMapping("/clear")
    public String clearAllData(RedirectAttributes redirectAttributes) {
        logger.info("Clearing all data");
        
        try {
            csvDataLoadingService.clearAllData();
            redirectAttributes.addFlashAttribute("success", "All data cleared successfully");
            logger.info("All data cleared successfully");
            
        } catch (Exception e) {
            logger.error("Error clearing data", e);
            redirectAttributes.addFlashAttribute("error", "Failed to clear data: " + e.getMessage());
        }
        
        return "redirect:/data/manage";
    }
}
