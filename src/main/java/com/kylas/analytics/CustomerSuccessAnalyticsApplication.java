package com.kylas.analytics;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Main application class for Customer Success Analytics.
 * 
 * This application provides daily usage reporting and analytics
 * similar to Mixpanel functionality for customer success tracking.
 * 
 * <AUTHOR>
 * @version 1.0
 */
@SpringBootApplication
@EnableCaching
@EnableAsync
@EnableScheduling
public class CustomerSuccessAnalyticsApplication {

    public static void main(String[] args) {
        SpringApplication.run(CustomerSuccessAnalyticsApplication.class, args);
    }
}
