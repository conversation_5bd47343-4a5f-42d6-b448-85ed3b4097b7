package com.kylas.analytics.service;

import com.kylas.analytics.domain.entity.TenantOnboarding;
import com.kylas.analytics.domain.entity.UserUsage;
import com.kylas.analytics.domain.repository.TenantOnboardingRepository;
import com.kylas.analytics.domain.repository.UserUsageRepository;
import com.kylas.analytics.service.csv.CsvDataProcessor;
import com.kylas.analytics.service.csv.CsvProcessingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Service for loading CSV data into the database.
 * Follows Single Responsibility Principle by handling only CSV data loading operations.
 * Implements Dependency Inversion Principle by depending on abstractions (interfaces).
 */
@Service
@Transactional
public class CsvDataLoadingService {

    private static final Logger logger = LoggerFactory.getLogger(CsvDataLoadingService.class);

    private final TenantOnboardingRepository tenantOnboardingRepository;
    private final UserUsageRepository userUsageRepository;
    private final CsvDataProcessor<TenantOnboarding> tenantCsvProcessor;
    private final CsvDataProcessor<UserUsage> userCsvProcessor;
    private final int batchSize;

    @Autowired
    public CsvDataLoadingService(
            TenantOnboardingRepository tenantOnboardingRepository,
            UserUsageRepository userUsageRepository,
            CsvDataProcessor<TenantOnboarding> tenantCsvProcessor,
            CsvDataProcessor<UserUsage> userCsvProcessor,
            @Value("${app.csv.batch-size:1000}") int batchSize) {
        this.tenantOnboardingRepository = tenantOnboardingRepository;
        this.userUsageRepository = userUsageRepository;
        this.tenantCsvProcessor = tenantCsvProcessor;
        this.userCsvProcessor = userCsvProcessor;
        this.batchSize = batchSize;
    }

    /**
     * Load tenant onboarding data from CSV file
     * 
     * @param csvFilePath Path to the CSV file
     * @return Number of records loaded
     * @throws CsvProcessingException if loading fails
     */
    public int loadTenantOnboardingData(String csvFilePath) throws CsvProcessingException {
        logger.info("Starting to load tenant onboarding data from: {}", csvFilePath);
        
        if (!tenantCsvProcessor.validateCsvFile(csvFilePath)) {
            throw new CsvProcessingException("CSV file validation failed: " + csvFilePath);
        }
        
        List<TenantOnboarding> tenants = tenantCsvProcessor.processCsvFile(csvFilePath);
        
        if (tenants.isEmpty()) {
            logger.warn("No tenant onboarding data found in CSV file: {}", csvFilePath);
            return 0;
        }
        
        int totalSaved = 0;
        
        // Process in batches for better performance
        for (int i = 0; i < tenants.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, tenants.size());
            List<TenantOnboarding> batch = tenants.subList(i, endIndex);
            
            try {
                List<TenantOnboarding> savedBatch = tenantOnboardingRepository.saveAll(batch);
                totalSaved += savedBatch.size();
                logger.debug("Saved batch of {} tenant onboarding records", savedBatch.size());
            } catch (Exception e) {
                logger.error("Error saving batch of tenant onboarding records", e);
                throw new CsvProcessingException("Failed to save tenant onboarding data", e);
            }
        }
        
        logger.info("Successfully loaded {} tenant onboarding records", totalSaved);
        return totalSaved;
    }

    /**
     * Load user usage data from CSV file
     * 
     * @param csvFilePath Path to the CSV file
     * @return Number of records loaded
     * @throws CsvProcessingException if loading fails
     */
    public int loadUserUsageData(String csvFilePath) throws CsvProcessingException {
        logger.info("Starting to load user usage data from: {}", csvFilePath);
        
        if (!userCsvProcessor.validateCsvFile(csvFilePath)) {
            throw new CsvProcessingException("CSV file validation failed: " + csvFilePath);
        }
        
        List<UserUsage> users = userCsvProcessor.processCsvFile(csvFilePath);
        
        if (users.isEmpty()) {
            logger.warn("No user usage data found in CSV file: {}", csvFilePath);
            return 0;
        }
        
        int totalSaved = 0;
        
        // Process in batches for better performance
        for (int i = 0; i < users.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, users.size());
            List<UserUsage> batch = users.subList(i, endIndex);
            
            try {
                List<UserUsage> savedBatch = userUsageRepository.saveAll(batch);
                totalSaved += savedBatch.size();
                logger.debug("Saved batch of {} user usage records", savedBatch.size());
            } catch (Exception e) {
                logger.error("Error saving batch of user usage records", e);
                throw new CsvProcessingException("Failed to save user usage data", e);
            }
        }
        
        logger.info("Successfully loaded {} user usage records", totalSaved);
        return totalSaved;
    }

    /**
     * Load both tenant onboarding and user usage data
     * 
     * @param tenantCsvPath Path to tenant onboarding CSV
     * @param userCsvPath Path to user usage CSV
     * @return Total number of records loaded
     * @throws CsvProcessingException if loading fails
     */
    public int loadAllData(String tenantCsvPath, String userCsvPath) throws CsvProcessingException {
        logger.info("Starting to load all CSV data");
        
        int tenantRecords = loadTenantOnboardingData(tenantCsvPath);
        int userRecords = loadUserUsageData(userCsvPath);
        
        int totalRecords = tenantRecords + userRecords;
        logger.info("Successfully loaded {} total records ({} tenants, {} users)", 
                   totalRecords, tenantRecords, userRecords);
        
        return totalRecords;
    }

    /**
     * Clear all existing data before loading new data
     */
    public void clearAllData() {
        logger.info("Clearing all existing data");
        userUsageRepository.deleteAll();
        tenantOnboardingRepository.deleteAll();
        logger.info("All data cleared successfully");
    }

    /**
     * Get loading statistics
     * 
     * @return Array containing [tenantCount, userCount]
     */
    public long[] getDataCounts() {
        long tenantCount = tenantOnboardingRepository.count();
        long userCount = userUsageRepository.count();
        return new long[]{tenantCount, userCount};
    }
}
