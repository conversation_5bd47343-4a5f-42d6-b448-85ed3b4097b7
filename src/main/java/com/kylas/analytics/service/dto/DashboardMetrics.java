package com.kylas.analytics.service.dto;

import java.util.List;
import java.util.Map;

/**
 * DTO for dashboard metrics data.
 * Contains aggregated analytics data for the main dashboard.
 */
public class DashboardMetrics {
    
    private long totalTenants;
    private long activeTenants;
    private long totalUsers;
    private long activeUsers;
    private long dailyActiveUsers;
    private double userEngagementRate;
    private double tenantRetentionRate;
    
    private List<TrendData> tenantSignupTrend;
    private List<TrendData> userActivityTrend;
    private Map<String, Long> industryDistribution;
    private Map<String, Long> planDistribution;
    private List<TopTenant> topTenantsByActivity;
    private List<TopUser> topUsersByActivity;
    
    // Constructors
    public DashboardMetrics() {}
    
    public DashboardMetrics(long totalTenants, long activeTenants, long totalUsers, 
                           long activeUsers, long dailyActiveUsers) {
        this.totalTenants = totalTenants;
        this.activeTenants = activeTenants;
        this.totalUsers = totalUsers;
        this.activeUsers = activeUsers;
        this.dailyActiveUsers = dailyActiveUsers;
    }
    
    // Getters and Setters
    public long getTotalTenants() { return totalTenants; }
    public void setTotalTenants(long totalTenants) { this.totalTenants = totalTenants; }
    
    public long getActiveTenants() { return activeTenants; }
    public void setActiveTenants(long activeTenants) { this.activeTenants = activeTenants; }
    
    public long getTotalUsers() { return totalUsers; }
    public void setTotalUsers(long totalUsers) { this.totalUsers = totalUsers; }
    
    public long getActiveUsers() { return activeUsers; }
    public void setActiveUsers(long activeUsers) { this.activeUsers = activeUsers; }
    
    public long getDailyActiveUsers() { return dailyActiveUsers; }
    public void setDailyActiveUsers(long dailyActiveUsers) { this.dailyActiveUsers = dailyActiveUsers; }
    
    public double getUserEngagementRate() { return userEngagementRate; }
    public void setUserEngagementRate(double userEngagementRate) { this.userEngagementRate = userEngagementRate; }
    
    public double getTenantRetentionRate() { return tenantRetentionRate; }
    public void setTenantRetentionRate(double tenantRetentionRate) { this.tenantRetentionRate = tenantRetentionRate; }
    
    public List<TrendData> getTenantSignupTrend() { return tenantSignupTrend; }
    public void setTenantSignupTrend(List<TrendData> tenantSignupTrend) { this.tenantSignupTrend = tenantSignupTrend; }
    
    public List<TrendData> getUserActivityTrend() { return userActivityTrend; }
    public void setUserActivityTrend(List<TrendData> userActivityTrend) { this.userActivityTrend = userActivityTrend; }
    
    public Map<String, Long> getIndustryDistribution() { return industryDistribution; }
    public void setIndustryDistribution(Map<String, Long> industryDistribution) { this.industryDistribution = industryDistribution; }
    
    public Map<String, Long> getPlanDistribution() { return planDistribution; }
    public void setPlanDistribution(Map<String, Long> planDistribution) { this.planDistribution = planDistribution; }
    
    public List<TopTenant> getTopTenantsByActivity() { return topTenantsByActivity; }
    public void setTopTenantsByActivity(List<TopTenant> topTenantsByActivity) { this.topTenantsByActivity = topTenantsByActivity; }
    
    public List<TopUser> getTopUsersByActivity() { return topUsersByActivity; }
    public void setTopUsersByActivity(List<TopUser> topUsersByActivity) { this.topUsersByActivity = topUsersByActivity; }
    
    /**
     * Inner class for trend data
     */
    public static class TrendData {
        private String date;
        private long count;
        
        public TrendData() {}
        
        public TrendData(String date, long count) {
            this.date = date;
            this.count = count;
        }
        
        public String getDate() { return date; }
        public void setDate(String date) { this.date = date; }
        
        public long getCount() { return count; }
        public void setCount(long count) { this.count = count; }
    }
    
    /**
     * Inner class for top tenant data
     */
    public static class TopTenant {
        private String tenantId;
        private String tenantName;
        private String industry;
        private long totalActivity;
        private long activeUsers;
        
        public TopTenant() {}
        
        public TopTenant(String tenantId, String tenantName, String industry, 
                        long totalActivity, long activeUsers) {
            this.tenantId = tenantId;
            this.tenantName = tenantName;
            this.industry = industry;
            this.totalActivity = totalActivity;
            this.activeUsers = activeUsers;
        }
        
        public String getTenantId() { return tenantId; }
        public void setTenantId(String tenantId) { this.tenantId = tenantId; }
        
        public String getTenantName() { return tenantName; }
        public void setTenantName(String tenantName) { this.tenantName = tenantName; }
        
        public String getIndustry() { return industry; }
        public void setIndustry(String industry) { this.industry = industry; }
        
        public long getTotalActivity() { return totalActivity; }
        public void setTotalActivity(long totalActivity) { this.totalActivity = totalActivity; }
        
        public long getActiveUsers() { return activeUsers; }
        public void setActiveUsers(long activeUsers) { this.activeUsers = activeUsers; }
    }
    
    /**
     * Inner class for top user data
     */
    public static class TopUser {
        private String userId;
        private String fullName;
        private String email;
        private String tenantName;
        private long totalActivity;
        private boolean isDAU;
        
        public TopUser() {}
        
        public TopUser(String userId, String fullName, String email, 
                      String tenantName, long totalActivity, boolean isDAU) {
            this.userId = userId;
            this.fullName = fullName;
            this.email = email;
            this.tenantName = tenantName;
            this.totalActivity = totalActivity;
            this.isDAU = isDAU;
        }
        
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
        
        public String getFullName() { return fullName; }
        public void setFullName(String fullName) { this.fullName = fullName; }
        
        public String getEmail() { return email; }
        public void setEmail(String email) { this.email = email; }
        
        public String getTenantName() { return tenantName; }
        public void setTenantName(String tenantName) { this.tenantName = tenantName; }
        
        public long getTotalActivity() { return totalActivity; }
        public void setTotalActivity(long totalActivity) { this.totalActivity = totalActivity; }
        
        public boolean isDAU() { return isDAU; }
        public void setDAU(boolean DAU) { isDAU = DAU; }
    }
}
