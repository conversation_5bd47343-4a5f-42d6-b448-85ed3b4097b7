package com.kylas.analytics.service;

import com.kylas.analytics.domain.entity.TenantOnboarding;
import com.kylas.analytics.domain.entity.UserUsage;
import com.kylas.analytics.domain.repository.TenantOnboardingRepository;
import com.kylas.analytics.domain.repository.UserUsageRepository;
import com.kylas.analytics.service.dto.DashboardMetrics;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Service for analytics and reporting functionality.
 * Provides business logic for generating insights similar to Mixpanel.
 * Follows Single Responsibility Principle by handling only analytics operations.
 */
@Service
@Transactional(readOnly = true)
public class AnalyticsService {

    private static final Logger logger = LoggerFactory.getLogger(AnalyticsService.class);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private final TenantOnboardingRepository tenantOnboardingRepository;
    private final UserUsageRepository userUsageRepository;

    @Autowired
    public AnalyticsService(TenantOnboardingRepository tenantOnboardingRepository,
                           UserUsageRepository userUsageRepository) {
        this.tenantOnboardingRepository = tenantOnboardingRepository;
        this.userUsageRepository = userUsageRepository;
    }

    /**
     * Get comprehensive dashboard metrics
     * 
     * @return DashboardMetrics containing all key metrics
     */
    @Cacheable("dashboardMetrics")
    public DashboardMetrics getDashboardMetrics() {
        logger.info("Generating dashboard metrics");
        
        DashboardMetrics metrics = new DashboardMetrics();
        
        // Basic counts
        metrics.setTotalTenants(tenantOnboardingRepository.count());
        metrics.setActiveTenants(tenantOnboardingRepository.countByStatus("active"));
        metrics.setTotalUsers(userUsageRepository.count());
        
        // Active users count
        long activeUsers = userUsageRepository.findByActive(true).size();
        metrics.setActiveUsers(activeUsers);
        
        // Daily active users
        long dauUsers = userUsageRepository.findByDau(true).size();
        metrics.setDailyActiveUsers(dauUsers);
        
        // Calculate engagement rate
        if (metrics.getTotalUsers() > 0) {
            metrics.setUserEngagementRate((double) dauUsers / metrics.getTotalUsers() * 100);
        }
        
        // Calculate retention rate
        if (metrics.getTotalTenants() > 0) {
            metrics.setTenantRetentionRate((double) metrics.getActiveTenants() / metrics.getTotalTenants() * 100);
        }
        
        // Get trend data
        LocalDateTime endDate = LocalDateTime.now();
        LocalDateTime startDate = endDate.minusDays(30);
        
        metrics.setTenantSignupTrend(getTenantSignupTrend(startDate, endDate));
        metrics.setUserActivityTrend(getUserActivityTrend(startDate, endDate));
        
        // Get distribution data
        metrics.setIndustryDistribution(getIndustryDistribution());
        metrics.setPlanDistribution(getPlanDistribution());
        
        // Get top performers
        metrics.setTopTenantsByActivity(getTopTenantsByActivity(10));
        metrics.setTopUsersByActivity(getTopUsersByActivity(10));
        
        logger.info("Dashboard metrics generated successfully");
        return metrics;
    }

    /**
     * Get tenant signup trend data
     */
    public List<DashboardMetrics.TrendData> getTenantSignupTrend(LocalDateTime startDate, LocalDateTime endDate) {
        List<Object[]> results = tenantOnboardingRepository.getTenantSignupTrends(startDate, endDate);
        
        return results.stream()
                .map(row -> new DashboardMetrics.TrendData(
                        row[0].toString(),
                        ((Number) row[1]).longValue()
                ))
                .collect(Collectors.toList());
    }

    /**
     * Get user activity trend data
     */
    public List<DashboardMetrics.TrendData> getUserActivityTrend(LocalDateTime startDate, LocalDateTime endDate) {
        List<Object[]> results = userUsageRepository.getDAUTrend(startDate, endDate);
        
        return results.stream()
                .map(row -> new DashboardMetrics.TrendData(
                        row[0].toString(),
                        ((Number) row[1]).longValue()
                ))
                .collect(Collectors.toList());
    }

    /**
     * Get industry distribution
     */
    public Map<String, Long> getIndustryDistribution() {
        List<Object[]> results = tenantOnboardingRepository.getIndustryDistribution();
        
        return results.stream()
                .collect(Collectors.toMap(
                        row -> row[0] != null ? row[0].toString() : "Unknown",
                        row -> ((Number) row[1]).longValue(),
                        (existing, replacement) -> existing,
                        LinkedHashMap::new
                ));
    }

    /**
     * Get plan distribution
     */
    public Map<String, Long> getPlanDistribution() {
        List<Object[]> results = tenantOnboardingRepository.getPlanDistribution();
        
        return results.stream()
                .collect(Collectors.toMap(
                        row -> row[0] != null ? row[0].toString() : "Unknown",
                        row -> ((Number) row[1]).longValue(),
                        (existing, replacement) -> existing,
                        LinkedHashMap::new
                ));
    }

    /**
     * Get top tenants by activity
     */
    public List<DashboardMetrics.TopTenant> getTopTenantsByActivity(int limit) {
        List<TenantOnboarding> topTenants = tenantOnboardingRepository
                .getTopTenantsByActivity(PageRequest.of(0, limit));
        
        return topTenants.stream()
                .map(tenant -> new DashboardMetrics.TopTenant(
                        tenant.getTenantId(),
                        tenant.getTenantName(),
                        tenant.getTenantIndustry(),
                        calculateTenantActivity(tenant),
                        tenant.getActiveUserCount() != null ? tenant.getActiveUserCount() : 0
                ))
                .collect(Collectors.toList());
    }

    /**
     * Get top users by activity
     */
    public List<DashboardMetrics.TopUser> getTopUsersByActivity(int limit) {
        List<UserUsage> topUsers = userUsageRepository
                .getTopActiveUsers(PageRequest.of(0, limit));
        
        return topUsers.stream()
                .map(user -> new DashboardMetrics.TopUser(
                        user.getUserId(),
                        user.getFullName(),
                        user.getEmail(),
                        user.getTenantName(),
                        calculateUserActivity(user),
                        user.getDau() != null ? user.getDau() : false
                ))
                .collect(Collectors.toList());
    }

    /**
     * Get user engagement metrics
     */
    public Map<String, Object> getUserEngagementMetrics() {
        Object[] results = userUsageRepository.getUserEngagementMetrics();
        
        Map<String, Object> metrics = new HashMap<>();
        if (results != null && results.length >= 5) {
            metrics.put("emailConnected", ((Number) results[0]).longValue());
            metrics.put("calendarConnected", ((Number) results[1]).longValue());
            metrics.put("loggedInUsers", ((Number) results[2]).longValue());
            metrics.put("avgDashboards", ((Number) results[3]).doubleValue());
            metrics.put("avgAppsInstalled", ((Number) results[4]).doubleValue());
        }
        
        return metrics;
    }

    /**
     * Get marketplace app usage statistics
     */
    public Map<String, Long> getMarketplaceAppUsage() {
        List<Object[]> results = userUsageRepository.getMarketplaceAppUsageStats();
        
        Map<String, Long> appUsage = new LinkedHashMap<>();
        
        for (Object[] row : results) {
            String apps = row[0].toString();
            Long count = ((Number) row[1]).longValue();
            
            // Split comma-separated apps and count each one
            String[] appArray = apps.split(",");
            for (String app : appArray) {
                String trimmedApp = app.trim();
                if (!trimmedApp.isEmpty()) {
                    appUsage.merge(trimmedApp, count, Long::sum);
                }
            }
        }
        
        // Sort by usage count and return top 10
        return appUsage.entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .limit(10)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (e1, e2) -> e1,
                        LinkedHashMap::new
                ));
    }

    /**
     * Get user retention metrics
     */
    public Map<String, Long> getUserRetentionMetrics() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime recentDate = now.minusDays(7);  // Active in last 7 days
        LocalDateTime oldDate = now.minusDays(30);    // Active in last 30 days
        
        Object[] results = userUsageRepository.getUserRetentionMetrics(recentDate, oldDate);
        
        Map<String, Long> retention = new HashMap<>();
        if (results != null && results.length >= 3) {
            retention.put("recentlyActive", ((Number) results[0]).longValue());
            retention.put("moderatelyActive", ((Number) results[1]).longValue());
            retention.put("inactive", ((Number) results[2]).longValue());
        }
        
        return retention;
    }

    /**
     * Search tenants by name
     */
    public List<TenantOnboarding> searchTenants(String searchTerm) {
        if (searchTerm == null || searchTerm.trim().isEmpty()) {
            return Collections.emptyList();
        }
        return tenantOnboardingRepository.searchByTenantName(searchTerm.trim());
    }

    /**
     * Search users by name or email
     */
    public List<UserUsage> searchUsers(String searchTerm) {
        if (searchTerm == null || searchTerm.trim().isEmpty()) {
            return Collections.emptyList();
        }
        return userUsageRepository.searchUsers(searchTerm.trim());
    }

    /**
     * Calculate total activity for a tenant
     */
    private long calculateTenantActivity(TenantOnboarding tenant) {
        long activity = 0;
        if (tenant.getLeadCount() != null) activity += tenant.getLeadCount();
        if (tenant.getDealCount() != null) activity += tenant.getDealCount();
        if (tenant.getContactCount() != null) activity += tenant.getContactCount();
        if (tenant.getCallCount() != null) activity += tenant.getCallCount();
        if (tenant.getMeetingCount() != null) activity += tenant.getMeetingCount();
        if (tenant.getTaskCount() != null) activity += tenant.getTaskCount();
        return activity;
    }

    /**
     * Calculate total activity for a user
     */
    private long calculateUserActivity(UserUsage user) {
        long activity = 0;
        if (user.getCreatedLeadCount() != null) activity += user.getCreatedLeadCount();
        if (user.getCreatedDealCount() != null) activity += user.getCreatedDealCount();
        if (user.getCreatedContactCount() != null) activity += user.getCreatedContactCount();
        if (user.getCreatedTaskCount() != null) activity += user.getCreatedTaskCount();
        if (user.getCreatedNoteCount() != null) activity += user.getCreatedNoteCount();
        if (user.getCreatedMeetingCount() != null) activity += user.getCreatedMeetingCount();
        return activity;
    }
}
