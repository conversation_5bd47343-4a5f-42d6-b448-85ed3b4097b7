package com.kylas.analytics.service;

import com.kylas.analytics.domain.repository.TenantOnboardingRepository;
import com.kylas.analytics.domain.repository.UserUsageHistoryRepository;
import com.kylas.analytics.domain.repository.UserUsageV2Repository;
import com.kylas.analytics.service.dto.DashboardMetrics;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Analytics service that operates on historical data for reporting.
 * Follows Single Responsibility Principle by focusing on analytics and reporting.
 * Implements Dependency Inversion Principle by depending on repository abstractions.
 */
@Service
@Transactional(readOnly = true)
public class HistoricalAnalyticsService {

    private static final Logger logger = LoggerFactory.getLogger(HistoricalAnalyticsService.class);

    private final UserUsageHistoryRepository userUsageHistoryRepository;
    private final UserUsageV2Repository userUsageV2Repository;
    private final TenantOnboardingRepository tenantOnboardingRepository;

    public HistoricalAnalyticsService(UserUsageHistoryRepository userUsageHistoryRepository,
                                    UserUsageV2Repository userUsageV2Repository,
                                    TenantOnboardingRepository tenantOnboardingRepository) {
        this.userUsageHistoryRepository = userUsageHistoryRepository;
        this.userUsageV2Repository = userUsageV2Repository;
        this.tenantOnboardingRepository = tenantOnboardingRepository;
    }

    /**
     * Get comprehensive dashboard metrics from historical data
     */
    @Cacheable(value = "dashboardMetrics", key = "'historical'")
    public DashboardMetrics getDashboardMetrics() {
        logger.info("Generating dashboard metrics from historical data");
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime last30Days = now.minusDays(30);
        LocalDateTime last7Days = now.minusDays(7);
        
        DashboardMetrics metrics = new DashboardMetrics();
        
        // Basic counts (combine active and historical data)
        metrics.setTotalTenants(tenantOnboardingRepository.count());
        metrics.setActiveTenants(tenantOnboardingRepository.countByStatus("active"));
        metrics.setTotalUsers(userUsageV2Repository.countDistinctUsers() + getHistoricalUserCount());
        metrics.setActiveUsers(userUsageV2Repository.count());
        
        // DAU from historical data
        metrics.setDailyActiveUsers(getDAUFromHistoricalData(last7Days, now));
        
        // Calculate engagement and retention rates
        long totalUsers = metrics.getTotalUsers();
        if (totalUsers > 0) {
            metrics.setUserEngagementRate((double) metrics.getDailyActiveUsers() / totalUsers * 100);
        }
        
        if (metrics.getTotalTenants() > 0) {
            metrics.setTenantRetentionRate((double) metrics.getActiveTenants() / metrics.getTotalTenants() * 100);
        }
        
        // Trend data from historical records
        metrics.setTenantSignupTrend(getTenantSignupTrend(last30Days, now));
        metrics.setUserActivityTrend(getUserActivityTrend(last30Days, now));
        
        // Distribution data
        metrics.setIndustryDistribution(getIndustryDistribution());
        metrics.setPlanDistribution(getPlanDistribution());
        
        // Top performers from historical data
        metrics.setTopTenantsByActivity(getTopTenantsByActivity(10));
        metrics.setTopUsersByActivity(getTopUsersByActivity(10));
        
        logger.info("Dashboard metrics generated successfully");
        return metrics;
    }

    /**
     * Get DAU trend from historical data
     */
    @Cacheable(value = "dauTrend", key = "#startDate + '_' + #endDate")
    public List<DashboardMetrics.TrendData> getUserActivityTrend(LocalDateTime startDate, LocalDateTime endDate) {
        logger.debug("Getting user activity trend from {} to {}", startDate, endDate);
        
        List<Object[]> rawData = userUsageHistoryRepository.getDAUTrend(startDate, endDate);
        
        return rawData.stream()
            .map(row -> new DashboardMetrics.TrendData(
                row[0].toString(), // date
                ((Number) row[1]).longValue() // count
            ))
            .collect(Collectors.toList());
    }

    /**
     * Get tenant signup trend (from tenant onboarding data)
     */
    @Cacheable(value = "tenantTrend", key = "#startDate + '_' + #endDate")
    public List<DashboardMetrics.TrendData> getTenantSignupTrend(LocalDateTime startDate, LocalDateTime endDate) {
        logger.debug("Getting tenant signup trend from {} to {}", startDate, endDate);
        
        List<Object[]> rawData = tenantOnboardingRepository.getTenantSignupTrends(startDate, endDate);
        
        return rawData.stream()
            .map(row -> new DashboardMetrics.TrendData(
                row[0].toString(), // date
                ((Number) row[1]).longValue() // count
            ))
            .collect(Collectors.toList());
    }

    /**
     * Get user engagement metrics from historical data
     */
    @Cacheable(value = "engagementMetrics", key = "'historical'")
    public Map<String, Object> getUserEngagementMetrics() {
        logger.debug("Getting user engagement metrics from historical data");
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime last30Days = now.minusDays(30);
        
        Object[] rawData = userUsageHistoryRepository.getUserEngagementMetrics(last30Days, now);
        
        Map<String, Object> metrics = new HashMap<>();
        if (rawData != null && rawData.length >= 5) {
            metrics.put("emailConnected", rawData[0]);
            metrics.put("calendarConnected", rawData[1]);
            metrics.put("loggedInUsers", rawData[2]);
            metrics.put("avgDashboards", rawData[3]);
            metrics.put("avgAppsInstalled", rawData[4]);
        }
        
        return metrics;
    }

    /**
     * Get top users by activity from historical data
     */
    @Cacheable(value = "topUsers", key = "#limit")
    public List<DashboardMetrics.TopUser> getTopUsersByActivity(int limit) {
        logger.debug("Getting top {} users by activity from historical data", limit);
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime last30Days = now.minusDays(30);
        
        List<Object[]> rawData = userUsageHistoryRepository.getTopActiveUsers(
            last30Days, now, PageRequest.of(0, limit)
        );
        
        return rawData.stream()
            .map(row -> {
                DashboardMetrics.TopUser user = new DashboardMetrics.TopUser();
                user.setUserId((String) row[0]);
                user.setFullName((String) row[1]);
                user.setEmail((String) row[2]);
                user.setTenantName((String) row[3]);
                user.setTotalActivity(((Number) row[4]).longValue());
                user.setDAU(((Number) row[5]).intValue() == 1);
                return user;
            })
            .collect(Collectors.toList());
    }

    /**
     * Get top tenants by activity (combines current and historical data)
     */
    @Cacheable(value = "topTenants", key = "#limit")
    public List<DashboardMetrics.TopTenant> getTopTenantsByActivity(int limit) {
        logger.debug("Getting top {} tenants by activity", limit);
        
        // Get from current tenant onboarding data
        return tenantOnboardingRepository.getTopTenantsByActivity(PageRequest.of(0, limit))
            .stream()
            .map(tenant -> {
                DashboardMetrics.TopTenant topTenant = new DashboardMetrics.TopTenant();
                topTenant.setTenantId(tenant.getTenantId());
                topTenant.setTenantName(tenant.getTenantName());
                topTenant.setIndustry(tenant.getTenantIndustry());
                
                // Calculate total activity from various metrics
                long totalActivity = Optional.ofNullable(tenant.getLeadCount()).orElse(0L) +
                                   Optional.ofNullable(tenant.getDealCount()).orElse(0L) +
                                   Optional.ofNullable(tenant.getContactCount()).orElse(0L) +
                                   Optional.ofNullable(tenant.getTaskCount()).orElse(0L);
                
                topTenant.setTotalActivity(totalActivity);
                topTenant.setActiveUsers(Optional.ofNullable(tenant.getActiveUserCount()).orElse(0));
                
                return topTenant;
            })
            .collect(Collectors.toList());
    }

    /**
     * Get marketplace app usage statistics from historical data
     */
    @Cacheable(value = "appUsage", key = "'historical'")
    public Map<String, Long> getMarketplaceAppUsage() {
        logger.debug("Getting marketplace app usage from historical data");
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime last30Days = now.minusDays(30);
        
        List<Object[]> rawData = userUsageHistoryRepository.getMarketplaceAppUsageStats(last30Days, now);
        
        Map<String, Long> appUsage = new HashMap<>();
        
        for (Object[] row : rawData) {
            String apps = (String) row[2]; // nameOfMarketplaceAppsInstalled
            Long count = ((Number) row[3]).longValue();
            
            if (apps != null && !apps.trim().isEmpty()) {
                // Split comma-separated app names
                String[] appNames = apps.split(",");
                for (String appName : appNames) {
                    String trimmedName = appName.trim();
                    if (!trimmedName.isEmpty()) {
                        appUsage.merge(trimmedName, count, Long::sum);
                    }
                }
            }
        }
        
        return appUsage;
    }

    /**
     * Get user retention metrics from historical data
     */
    @Cacheable(value = "retentionMetrics", key = "'historical'")
    public Map<String, Long> getUserRetentionMetrics() {
        logger.debug("Getting user retention metrics from historical data");
        
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime recentDate = now.minusDays(7);
        LocalDateTime moderateDate = now.minusDays(30);
        
        Object[] rawData = userUsageHistoryRepository.getUserRetentionMetrics(recentDate, moderateDate);
        
        Map<String, Long> metrics = new HashMap<>();
        if (rawData != null && rawData.length >= 3) {
            metrics.put("recentlyActive", ((Number) rawData[0]).longValue());
            metrics.put("moderatelyActive", ((Number) rawData[1]).longValue());
            metrics.put("inactive", ((Number) rawData[2]).longValue());
        }
        
        return metrics;
    }

    /**
     * Get industry distribution from tenant data
     */
    private Map<String, Long> getIndustryDistribution() {
        List<Object[]> rawData = tenantOnboardingRepository.getIndustryDistribution();
        
        return rawData.stream()
            .collect(Collectors.toMap(
                row -> (String) row[0],
                row -> ((Number) row[1]).longValue()
            ));
    }

    /**
     * Get plan distribution from tenant data
     */
    private Map<String, Long> getPlanDistribution() {
        List<Object[]> rawData = tenantOnboardingRepository.getPlanDistribution();
        
        return rawData.stream()
            .collect(Collectors.toMap(
                row -> (String) row[0],
                row -> ((Number) row[1]).longValue()
            ));
    }

    /**
     * Get DAU count from historical data
     */
    private long getDAUFromHistoricalData(LocalDateTime startDate, LocalDateTime endDate) {
        List<Object[]> dauData = userUsageHistoryRepository.getDAUTrend(startDate, endDate);
        
        return dauData.stream()
            .mapToLong(row -> ((Number) row[1]).longValue())
            .sum();
    }

    /**
     * Get historical user count
     */
    private long getHistoricalUserCount() {
        // This could be optimized with a specific query
        return userUsageHistoryRepository.count();
    }

    /**
     * Search users in historical data
     */
    public List<Object[]> searchHistoricalUsers(String searchTerm) {
        logger.debug("Searching historical users with term: {}", searchTerm);
        
        return userUsageHistoryRepository.searchHistoricalRecords(searchTerm)
            .stream()
            .map(history -> new Object[]{
                history.getUserId(),
                history.getFullName(),
                history.getEmail(),
                history.getTenantName(),
                history.getUsagePublishedDate(),
                history.getArchivedAt()
            })
            .collect(Collectors.toList());
    }

    /**
     * Get usage trends by tenant from historical data
     */
    @Cacheable(value = "tenantUsageTrends", key = "#startDate + '_' + #endDate")
    public List<Object[]> getUsageTrendsByTenant(LocalDateTime startDate, LocalDateTime endDate) {
        logger.debug("Getting usage trends by tenant from {} to {}", startDate, endDate);
        
        return userUsageHistoryRepository.getUsageTrendsByTenant(startDate, endDate);
    }

    /**
     * Get aggregated metrics by time period
     */
    @Cacheable(value = "aggregatedMetrics", key = "#startDate + '_' + #endDate")
    public List<Object[]> getAggregatedMetricsByPeriod(LocalDateTime startDate, LocalDateTime endDate) {
        logger.debug("Getting aggregated metrics by period from {} to {}", startDate, endDate);
        
        return userUsageHistoryRepository.getAggregatedMetricsByPeriod(startDate, endDate);
    }
}
