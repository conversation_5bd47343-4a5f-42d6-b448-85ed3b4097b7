package com.kylas.analytics.service;

import com.kylas.analytics.domain.entity.UserUsageV2;
import com.kylas.analytics.domain.entity.UserUsageHistory;
import com.kylas.analytics.domain.repository.UserUsageV2Repository;
import com.kylas.analytics.domain.repository.UserUsageHistoryRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Service for managing data archiving and lifecycle.
 * Follows Single Responsibility Principle by focusing solely on data archiving operations.
 * Implements Dependency Inversion Principle by depending on repository abstractions.
 */
@Service
@Transactional
public class DataArchivingService {

    private static final Logger logger = LoggerFactory.getLogger(DataArchivingService.class);

    private final UserUsageV2Repository userUsageV2Repository;
    private final UserUsageHistoryRepository userUsageHistoryRepository;

    @Value("${app.data.archive.retention-days:90}")
    private int retentionDays;

    @Value("${app.data.archive.batch-size:1000}")
    private int batchSize;

    @Value("${app.data.archive.history-retention-days:365}")
    private int historyRetentionDays;

    public DataArchivingService(UserUsageV2Repository userUsageV2Repository,
                               UserUsageHistoryRepository userUsageHistoryRepository) {
        this.userUsageV2Repository = userUsageV2Repository;
        this.userUsageHistoryRepository = userUsageHistoryRepository;
    }

    /**
     * Archive old records from active table to history table
     */
    @Async
    public CompletableFuture<Integer> archiveOldRecords() {
        return archiveOldRecords("SCHEDULED_ARCHIVING");
    }

    /**
     * Archive old records with specific reason
     */
    @Async
    public CompletableFuture<Integer> archiveOldRecords(String archiveReason) {
        logger.info("Starting data archiving process with reason: {}", archiveReason);
        
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(retentionDays);
        List<UserUsageV2> recordsToArchive = userUsageV2Repository.findRecordsForArchiving(cutoffDate);
        
        int totalArchived = 0;
        int batchCount = 0;
        
        // Process in batches to avoid memory issues
        for (int i = 0; i < recordsToArchive.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, recordsToArchive.size());
            List<UserUsageV2> batch = recordsToArchive.subList(i, endIndex);
            
            int archivedInBatch = archiveBatch(batch, archiveReason);
            totalArchived += archivedInBatch;
            batchCount++;
            
            logger.debug("Archived batch {} with {} records", batchCount, archivedInBatch);
        }
        
        logger.info("Data archiving completed. Total records archived: {}", totalArchived);
        return CompletableFuture.completedFuture(totalArchived);
    }

    /**
     * Archive a specific batch of records
     */
    private int archiveBatch(List<UserUsageV2> records, String archiveReason) {
        try {
            // Create history records
            List<UserUsageHistory> historyRecords = records.stream()
                .map(record -> new UserUsageHistory(record, archiveReason))
                .toList();
            
            // Save to history table
            userUsageHistoryRepository.saveAll(historyRecords);
            
            // Delete from active table
            userUsageV2Repository.deleteAll(records);
            
            return records.size();
        } catch (Exception e) {
            logger.error("Error archiving batch of {} records", records.size(), e);
            throw new RuntimeException("Failed to archive batch", e);
        }
    }

    /**
     * Archive specific user records
     */
    public int archiveUserRecords(String userId, String archiveReason) {
        logger.info("Archiving records for user: {} with reason: {}", userId, archiveReason);
        
        List<UserUsageV2> userRecords = userUsageV2Repository.findByEmail(userId);
        if (userRecords.isEmpty()) {
            logger.warn("No records found for user: {}", userId);
            return 0;
        }
        
        return archiveBatch(userRecords, archiveReason);
    }

    /**
     * Archive records for a specific tenant
     */
    public int archiveTenantRecords(String tenantId, String archiveReason) {
        logger.info("Archiving records for tenant: {} with reason: {}", tenantId, archiveReason);
        
        List<UserUsageV2> tenantRecords = userUsageV2Repository.findByTenantId(tenantId);
        if (tenantRecords.isEmpty()) {
            logger.warn("No records found for tenant: {}", tenantId);
            return 0;
        }
        
        int totalArchived = 0;
        
        // Process in batches
        for (int i = 0; i < tenantRecords.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, tenantRecords.size());
            List<UserUsageV2> batch = tenantRecords.subList(i, endIndex);
            totalArchived += archiveBatch(batch, archiveReason);
        }
        
        return totalArchived;
    }

    /**
     * Clean up old history records beyond retention period
     */
    @Async
    public CompletableFuture<Integer> cleanupOldHistoryRecords() {
        logger.info("Starting cleanup of old history records");
        
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(historyRetentionDays);
        int deletedCount = userUsageHistoryRepository.deleteRecordsOlderThan(cutoffDate);
        
        logger.info("Cleanup completed. Deleted {} old history records", deletedCount);
        return CompletableFuture.completedFuture(deletedCount);
    }

    /**
     * Get archiving statistics
     */
    public ArchivingStatistics getArchivingStatistics() {
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime last30Days = now.minusDays(30);
        LocalDateTime last7Days = now.minusDays(7);
        
        long activeRecords = userUsageV2Repository.count();
        long historyRecords = userUsageHistoryRepository.count();
        long recentlyArchived = userUsageHistoryRepository.findByArchivedAtBetween(last7Days, now).size();
        long monthlyArchived = userUsageHistoryRepository.findByArchivedAtBetween(last30Days, now).size();
        
        return new ArchivingStatistics(activeRecords, historyRecords, recentlyArchived, monthlyArchived);
    }

    /**
     * Force archive all records older than specified days
     */
    public int forceArchiveOldRecords(int olderThanDays, String archiveReason) {
        logger.warn("Force archiving records older than {} days with reason: {}", olderThanDays, archiveReason);
        
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(olderThanDays);
        List<UserUsageV2> recordsToArchive = userUsageV2Repository.findRecordsForArchiving(cutoffDate);
        
        int totalArchived = 0;
        
        // Process in batches
        for (int i = 0; i < recordsToArchive.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, recordsToArchive.size());
            List<UserUsageV2> batch = recordsToArchive.subList(i, endIndex);
            totalArchived += archiveBatch(batch, archiveReason);
        }
        
        return totalArchived;
    }

    /**
     * Restore records from history back to active table
     */
    public int restoreRecords(List<Long> historyIds, String restoreReason) {
        logger.info("Restoring {} records from history with reason: {}", historyIds.size(), restoreReason);
        
        List<UserUsageHistory> historyRecords = userUsageHistoryRepository.findAllById(historyIds);
        
        List<UserUsageV2> restoredRecords = historyRecords.stream()
            .map(this::convertHistoryToActive)
            .toList();
        
        userUsageV2Repository.saveAll(restoredRecords);
        
        // Optionally delete from history or mark as restored
        // userUsageHistoryRepository.deleteAll(historyRecords);
        
        logger.info("Restored {} records to active table", restoredRecords.size());
        return restoredRecords.size();
    }

    /**
     * Convert history record back to active record
     */
    private UserUsageV2 convertHistoryToActive(UserUsageHistory history) {
        UserUsageV2 active = new UserUsageV2();
        active.setUserId(history.getUserId());
        active.setFullName(history.getFullName());
        active.setEmail(history.getEmail());
        active.setTenantId(history.getTenantId());
        active.setTenantName(history.getTenantName());
        active.setPlanName(history.getPlanName());
        active.setUsagePublishedDate(history.getUsagePublishedDate());
        active.setPrimaryPhoneNumber(history.getPrimaryPhoneNumber());
        active.setAllPhoneNumbers(history.getAllPhoneNumbers());
        active.setStatus(history.getStatus());
        active.setCreatedAt(history.getCreatedAt());
        active.setUpdatedAt(history.getUpdatedAt());
        active.setAdditionalData(history.getAdditionalData());
        return active;
    }

    /**
     * Data class for archiving statistics
     */
    public static class ArchivingStatistics {
        private final long activeRecords;
        private final long historyRecords;
        private final long recentlyArchived;
        private final long monthlyArchived;

        public ArchivingStatistics(long activeRecords, long historyRecords, 
                                 long recentlyArchived, long monthlyArchived) {
            this.activeRecords = activeRecords;
            this.historyRecords = historyRecords;
            this.recentlyArchived = recentlyArchived;
            this.monthlyArchived = monthlyArchived;
        }

        public long getActiveRecords() { return activeRecords; }
        public long getHistoryRecords() { return historyRecords; }
        public long getRecentlyArchived() { return recentlyArchived; }
        public long getMonthlyArchived() { return monthlyArchived; }
        public long getTotalRecords() { return activeRecords + historyRecords; }
        public double getArchiveRatio() { 
            return totalRecords() > 0 ? (double) historyRecords / totalRecords() : 0.0; 
        }
        
        private long totalRecords() { return activeRecords + historyRecords; }
    }
}
