package com.kylas.analytics.service.csv;

import com.kylas.analytics.domain.entity.TenantOnboarding;
import com.opencsv.CSVReader;
import com.opencsv.exceptions.CsvException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.FileReader;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;

/**
 * CSV processor for TenantOnboarding entities.
 * Implements Single Responsibility Principle by handling only tenant onboarding CSV processing.
 */
@Component
public class TenantOnboardingCsvProcessor implements CsvDataProcessor<TenantOnboarding> {

    private static final Logger logger = LoggerFactory.getLogger(TenantOnboardingCsvProcessor.class);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");

    @Override
    public List<TenantOnboarding> processCsvFile(String csvFilePath) throws CsvProcessingException {
        logger.info("Processing tenant onboarding CSV file: {}", csvFilePath);
        
        List<TenantOnboarding> tenants = new ArrayList<>();
        
        try (CSVReader reader = new CSVReader(new FileReader(csvFilePath))) {
            List<String[]> records = reader.readAll();
            
            if (records.isEmpty()) {
                throw new CsvProcessingException("CSV file is empty", csvFilePath, 0);
            }
            
            // Skip header row
            for (int i = 1; i < records.size(); i++) {
                String[] record = records.get(i);
                try {
                    TenantOnboarding tenant = mapRecordToEntity(record, i + 1);
                    tenants.add(tenant);
                } catch (Exception e) {
                    logger.warn("Error processing record at line {}: {}", i + 1, e.getMessage());
                    throw new CsvProcessingException("Failed to process record", csvFilePath, i + 1, e);
                }
            }
            
            logger.info("Successfully processed {} tenant onboarding records", tenants.size());
            return tenants;
            
        } catch (IOException | CsvException e) {
            throw new CsvProcessingException("Failed to read CSV file", e);
        }
    }

    @Override
    public boolean validateCsvFile(String csvFilePath) {
        try (CSVReader reader = new CSVReader(new FileReader(csvFilePath))) {
            List<String[]> records = reader.readAll();
            
            if (records.isEmpty()) {
                logger.warn("CSV file is empty: {}", csvFilePath);
                return false;
            }
            
            // Validate header
            String[] header = records.get(0);
            if (header.length < 4) { // Minimum required columns
                logger.warn("CSV file has insufficient columns: {}", csvFilePath);
                return false;
            }
            
            // Validate at least one data row
            if (records.size() < 2) {
                logger.warn("CSV file has no data rows: {}", csvFilePath);
                return false;
            }
            
            return true;
            
        } catch (IOException | CsvException e) {
            logger.error("Error validating CSV file: {}", csvFilePath, e);
            return false;
        }
    }

    @Override
    public Class<TenantOnboarding> getEntityClass() {
        return TenantOnboarding.class;
    }

    private TenantOnboarding mapRecordToEntity(String[] record, int lineNumber) throws CsvProcessingException {
        try {
            TenantOnboarding tenant = new TenantOnboarding();
            
            // Map CSV columns to entity fields
            tenant.setTenantId(getStringValue(record, 0));
            tenant.setTenantName(getStringValue(record, 1));
            tenant.setTenantIndustry(getStringValue(record, 2));
            tenant.setSignedUpAt(parseDateTime(getStringValue(record, 3)));
            tenant.setTenantUserEmail(getStringValue(record, 4));
            tenant.setProfileCount(getIntegerValue(record, 5));
            tenant.setAccountSettingsCompleted(getBooleanValue(record, 6));
            tenant.setActiveUserCount(getIntegerValue(record, 7));
            tenant.setInactiveUserCount(getIntegerValue(record, 8));
            tenant.setTeamCount(getIntegerValue(record, 9));
            tenant.setPlanName(getStringValue(record, 10));
            tenant.setStatus(getStringValue(record, 11));
            tenant.setNumberOfCustomDashboardsCreated(getIntegerValue(record, 12));
            tenant.setMessageCount(getLongValue(record, 13));
            tenant.setNumberOfMarketplaceAppsInstalled(getIntegerValue(record, 14));
            tenant.setNameOfMarketplaceAppsInstalled(getStringValue(record, 15));
            tenant.setDauTrue(getIntegerValue(record, 16));
            tenant.setDauFalse(getIntegerValue(record, 17));
            tenant.setLoggedInUsersCount(getIntegerValue(record, 18));
            tenant.setActiveWorkflowCount(getIntegerValue(record, 19));
            tenant.setActiveScoreRuleCount(getIntegerValue(record, 20));
            tenant.setActiveCustomFieldsCount(getIntegerValue(record, 21));
            tenant.setInactiveCustomFieldsCount(getIntegerValue(record, 22));
            tenant.setProductCount(getIntegerValue(record, 23));
            tenant.setImportCount(getIntegerValue(record, 24));
            tenant.setPipelineCount(getIntegerValue(record, 25));
            tenant.setLeadCount(getLongValue(record, 26));
            tenant.setDealCount(getLongValue(record, 27));
            tenant.setContactCount(getLongValue(record, 28));
            tenant.setCompanyCount(getLongValue(record, 29));
            tenant.setCallCount(getLongValue(record, 30));
            tenant.setMeetingCount(getLongValue(record, 31));
            tenant.setTaskCount(getLongValue(record, 32));
            tenant.setNoteCount(getLongValue(record, 33));
            tenant.setCreatedQuoteCount(getLongValue(record, 34));
            tenant.setUpdatedQuoteCount(getLongValue(record, 35));
            tenant.setActiveLayoutCount(getIntegerValue(record, 36));
            tenant.setInactiveLayoutCount(getIntegerValue(record, 37));
            tenant.setCreateLeadLayoutCount(getIntegerValue(record, 38));
            tenant.setEditLeadLayoutCount(getIntegerValue(record, 39));
            tenant.setGoalTrialActive(getStringValue(record, 40));
            tenant.setGoalAddon(getStringValue(record, 41));
            tenant.setNumberOfGoalsCreated(getIntegerValue(record, 42));
            tenant.setSubscriptionId(getStringValue(record, 43));
            tenant.setCustomerId(getStringValue(record, 44));
            
            return tenant;
            
        } catch (Exception e) {
            throw new CsvProcessingException("Failed to map record to entity", "", lineNumber, e);
        }
    }

    private String getStringValue(String[] record, int index) {
        if (index >= record.length || record[index] == null || record[index].trim().isEmpty()) {
            return null;
        }
        return record[index].trim().replace("\"", "");
    }

    private Integer getIntegerValue(String[] record, int index) {
        String value = getStringValue(record, index);
        if (value == null) return null;
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    private Long getLongValue(String[] record, int index) {
        String value = getStringValue(record, index);
        if (value == null) return null;
        try {
            return Long.parseLong(value);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    private Boolean getBooleanValue(String[] record, int index) {
        String value = getStringValue(record, index);
        if (value == null) return null;
        return "true".equalsIgnoreCase(value) || "1".equals(value);
    }

    private LocalDateTime parseDateTime(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        try {
            return LocalDateTime.parse(dateStr, DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            logger.warn("Failed to parse date: {}", dateStr);
            return null;
        }
    }
}
