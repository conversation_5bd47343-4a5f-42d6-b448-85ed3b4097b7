package com.kylas.analytics.service.csv;

import java.util.List;

/**
 * Interface for processing CSV data.
 * Follows Single Responsibility Principle by defining a contract for CSV processing.
 * 
 * @param <T> The entity type to process
 */
public interface CsvDataProcessor<T> {
    
    /**
     * Process CSV data and convert to entities
     * 
     * @param csvFilePath Path to the CSV file
     * @return List of processed entities
     * @throws CsvProcessingException if processing fails
     */
    List<T> processCsvFile(String csvFilePath) throws CsvProcessingException;
    
    /**
     * Validate CSV data before processing
     * 
     * @param csvFilePath Path to the CSV file
     * @return true if valid, false otherwise
     */
    boolean validateCsvFile(String csvFilePath);
    
    /**
     * Get the entity class this processor handles
     * 
     * @return Class of the entity
     */
    Class<T> getEntityClass();
}
