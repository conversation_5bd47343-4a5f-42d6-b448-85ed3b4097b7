package com.kylas.analytics.service.csv;

/**
 * Custom exception for CSV processing errors.
 * Provides specific error handling for CSV-related operations.
 */
public class CsvProcessingException extends Exception {
    
    private final String fileName;
    private final int lineNumber;
    
    public CsvProcessingException(String message) {
        super(message);
        this.fileName = null;
        this.lineNumber = -1;
    }
    
    public CsvProcessingException(String message, Throwable cause) {
        super(message, cause);
        this.fileName = null;
        this.lineNumber = -1;
    }
    
    public CsvProcessingException(String message, String fileName, int lineNumber) {
        super(String.format("Error processing file '%s' at line %d: %s", fileName, lineNumber, message));
        this.fileName = fileName;
        this.lineNumber = lineNumber;
    }
    
    public CsvProcessingException(String message, String fileName, int lineNumber, Throwable cause) {
        super(String.format("Error processing file '%s' at line %d: %s", fileName, lineNumber, message), cause);
        this.fileName = fileName;
        this.lineNumber = lineNumber;
    }
    
    public String getFileName() {
        return fileName;
    }
    
    public int getLineNumber() {
        return lineNumber;
    }
}
