package com.kylas.analytics.service.csv;

import com.kylas.analytics.domain.entity.UserUsage;
import com.opencsv.CSVReader;
import com.opencsv.exceptions.CsvException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.FileReader;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.List;

/**
 * CSV processor for UserUsage entities.
 * Implements Single Responsibility Principle by handling only user usage CSV processing.
 */
@Component
public class UserUsageCsvProcessor implements CsvDataProcessor<UserUsage> {

    private static final Logger logger = LoggerFactory.getLogger(UserUsageCsvProcessor.class);
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");

    @Override
    public List<UserUsage> processCsvFile(String csvFilePath) throws CsvProcessingException {
        logger.info("Processing user usage CSV file: {}", csvFilePath);
        
        List<UserUsage> users = new ArrayList<>();
        
        try (CSVReader reader = new CSVReader(new FileReader(csvFilePath))) {
            List<String[]> records = reader.readAll();
            
            if (records.isEmpty()) {
                throw new CsvProcessingException("CSV file is empty", csvFilePath, 0);
            }
            
            // Skip header row
            for (int i = 1; i < records.size(); i++) {
                String[] record = records.get(i);
                try {
                    UserUsage user = mapRecordToEntity(record, i + 1);
                    users.add(user);
                } catch (Exception e) {
                    logger.warn("Error processing record at line {}: {}", i + 1, e.getMessage());
                    throw new CsvProcessingException("Failed to process record", csvFilePath, i + 1, e);
                }
            }
            
            logger.info("Successfully processed {} user usage records", users.size());
            return users;
            
        } catch (IOException | CsvException e) {
            throw new CsvProcessingException("Failed to read CSV file", e);
        }
    }

    @Override
    public boolean validateCsvFile(String csvFilePath) {
        try (CSVReader reader = new CSVReader(new FileReader(csvFilePath))) {
            List<String[]> records = reader.readAll();
            
            if (records.isEmpty()) {
                logger.warn("CSV file is empty: {}", csvFilePath);
                return false;
            }
            
            // Validate header
            String[] header = records.get(0);
            if (header.length < 5) { // Minimum required columns
                logger.warn("CSV file has insufficient columns: {}", csvFilePath);
                return false;
            }
            
            // Validate at least one data row
            if (records.size() < 2) {
                logger.warn("CSV file has no data rows: {}", csvFilePath);
                return false;
            }
            
            return true;
            
        } catch (IOException | CsvException e) {
            logger.error("Error validating CSV file: {}", csvFilePath, e);
            return false;
        }
    }

    @Override
    public Class<UserUsage> getEntityClass() {
        return UserUsage.class;
    }

    private UserUsage mapRecordToEntity(String[] record, int lineNumber) throws CsvProcessingException {
        try {
            UserUsage user = new UserUsage();
            
            // Map CSV columns to entity fields
            user.setUserId(getStringValue(record, 0));
            user.setFullName(getStringValue(record, 1));
            user.setEmail(getStringValue(record, 2));
            user.setTenantId(getStringValue(record, 3));
            user.setTenantName(getStringValue(record, 4));
            user.setPlanName(getStringValue(record, 5));
            user.setUsagePublishedDate(parseDateTime(getStringValue(record, 6)));
            user.setPrimaryPhoneNumber(getStringValue(record, 7));
            user.setAllPhoneNumbers(getStringValue(record, 8));
            user.setStatus(getStringValue(record, 9));
            user.setCreatedAt(parseDateTime(getStringValue(record, 10)));
            user.setUpdatedAt(parseDateTime(getStringValue(record, 11)));
            user.setLastLoginAt(parseDateTime(getStringValue(record, 12)));
            user.setDeactivatedAt(parseDateTime(getStringValue(record, 13)));
            user.setActive(getBooleanValue(record, 14));
            user.setVerified(getBooleanValue(record, 15));
            user.setCreatedLeadCount(getIntegerValue(record, 16));
            user.setUpdatedLeadCount(getIntegerValue(record, 17));
            user.setCreatedDealCount(getIntegerValue(record, 18));
            user.setUpdatedDealCount(getIntegerValue(record, 19));
            user.setCreatedContactCount(getIntegerValue(record, 20));
            user.setUpdatedContactCount(getIntegerValue(record, 21));
            user.setCreatedTaskCount(getIntegerValue(record, 22));
            user.setCreatedNoteCount(getIntegerValue(record, 23));
            user.setCreatedMeetingCount(getIntegerValue(record, 24));
            user.setCreatedCompanyCount(getIntegerValue(record, 25));
            user.setUpdatedCompanyCount(getIntegerValue(record, 26));
            user.setEmailAccountConnected(getBooleanValue(record, 27));
            user.setEmailConnectedAccountName(getStringValue(record, 28));
            user.setCalendarAccountConnected(getBooleanValue(record, 29));
            user.setConnectedCalendarAccountName(getStringValue(record, 30));
            user.setLoggedIn(getBooleanValue(record, 31));
            user.setCallsLogged(getIntegerValue(record, 32));
            user.setEmailsSent(getIntegerValue(record, 33));
            user.setNumberOfCustomDashboardsCreated(getIntegerValue(record, 34));
            user.setMessageCount(getLongValue(record, 35));
            user.setNumberOfMarketplaceAppsInstalled(getIntegerValue(record, 36));
            user.setNameOfMarketplaceAppsInstalled(getStringValue(record, 37));
            user.setCreatedQuoteCount(getIntegerValue(record, 38));
            user.setUpdatedQuoteCount(getIntegerValue(record, 39));
            user.setDau(getBooleanValue(record, 40));
            
            return user;
            
        } catch (Exception e) {
            throw new CsvProcessingException("Failed to map record to entity", "", lineNumber, e);
        }
    }

    private String getStringValue(String[] record, int index) {
        if (index >= record.length || record[index] == null || record[index].trim().isEmpty()) {
            return null;
        }
        return record[index].trim().replace("\"", "");
    }

    private Integer getIntegerValue(String[] record, int index) {
        String value = getStringValue(record, index);
        if (value == null) return null;
        try {
            return Integer.parseInt(value);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    private Long getLongValue(String[] record, int index) {
        String value = getStringValue(record, index);
        if (value == null) return null;
        try {
            return Long.parseLong(value);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    private Boolean getBooleanValue(String[] record, int index) {
        String value = getStringValue(record, index);
        if (value == null) return null;
        return "true".equalsIgnoreCase(value) || "1".equals(value);
    }

    private LocalDateTime parseDateTime(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        try {
            return LocalDateTime.parse(dateStr, DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            logger.warn("Failed to parse date: {}", dateStr);
            return null;
        }
    }
}
