package com.kylas.analytics.service.csv;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.kylas.analytics.domain.entity.UserUsageV2;
import com.opencsv.CSVReader;
import com.opencsv.exceptions.CsvException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.FileReader;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;

/**
 * CSV processor for UserUsageV2 entity with JSONB support.
 * Follows Single Responsibility Principle by handling only CSV processing for user usage data.
 * Implements Open/Closed Principle by being extensible for new data fields without modification.
 */
@Component
public class UserUsageV2CsvProcessor implements CsvDataProcessor<UserUsageV2> {

    private static final Logger logger = LoggerFactory.getLogger(UserUsageV2CsvProcessor.class);
    
    private final ObjectMapper objectMapper;

    // Core columns that map directly to entity fields
    private static final Set<String> CORE_COLUMNS = Set.of(
        "userId", "fullName", "email", "tenantId", "tenantName", "planName",
        "usagePublishedDate", "primaryPhoneNumber", "allPhoneNumbers", 
        "status", "createdAt", "updatedAt"
    );

    // Date formatters for parsing various date formats
    private static final List<DateTimeFormatter> DATE_FORMATTERS = Arrays.asList(
        DateTimeFormatter.ISO_LOCAL_DATE_TIME,
        DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"),
        DateTimeFormatter.ofPattern("yyyy-MM-dd")
    );

    public UserUsageV2CsvProcessor(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Override
    public List<UserUsageV2> processCsvFile(String filePath) throws CsvProcessingException {
        logger.info("Processing CSV file: {}", filePath);
        
        List<UserUsageV2> userUsageList = new ArrayList<>();
        
        try (CSVReader reader = new CSVReader(new FileReader(filePath))) {
            List<String[]> records = reader.readAll();
            
            if (records.isEmpty()) {
                throw new CsvProcessingException("CSV file is empty: " + filePath);
            }
            
            String[] headers = records.get(0);
            validateHeaders(headers);
            
            Map<String, Integer> headerIndexMap = createHeaderIndexMap(headers);
            
            // Process data rows (skip header)
            for (int i = 1; i < records.size(); i++) {
                String[] row = records.get(i);
                try {
                    UserUsageV2 userUsage = processRow(row, headerIndexMap, headers);
                    if (userUsage != null) {
                        userUsageList.add(userUsage);
                    }
                } catch (Exception e) {
                    logger.error("Error processing row {}: {}", i + 1, e.getMessage());
                    throw new CsvProcessingException("Failed to process record at row " + (i + 1), e);
                }
            }
            
            logger.info("Successfully processed {} user usage records", userUsageList.size());
            return userUsageList;
            
        } catch (IOException | CsvException e) {
            logger.error("Error reading CSV file: {}", filePath, e);
            throw new CsvProcessingException("Failed to read CSV file: " + filePath, e);
        }
    }

    @Override
    public boolean validateCsvFile(String filePath) {
        try (CSVReader reader = new CSVReader(new FileReader(filePath))) {
            List<String[]> records = reader.readAll();
            
            if (records.isEmpty()) {
                logger.warn("CSV file is empty: {}", filePath);
                return false;
            }
            
            String[] headers = records.get(0);
            
            // Check for required core columns
            Set<String> headerSet = Set.of(headers);
            Set<String> requiredColumns = Set.of("userId", "fullName", "email", "tenantId", "usagePublishedDate");
            
            for (String required : requiredColumns) {
                if (!headerSet.contains(required)) {
                    logger.warn("Missing required column '{}' in CSV file: {}", required, filePath);
                    return false;
                }
            }
            
            // Must have at least one data row
            if (records.size() < 2) {
                logger.warn("CSV file has no data rows: {}", filePath);
                return false;
            }
            
            return true;
            
        } catch (Exception e) {
            logger.error("Error validating CSV file: {}", filePath, e);
            return false;
        }
    }

    @Override
    public Class<UserUsageV2> getEntityClass() {
        return UserUsageV2.class;
    }

    /**
     * Validate CSV headers
     */
    private void validateHeaders(String[] headers) throws CsvProcessingException {
        if (headers.length < 5) {
            throw new CsvProcessingException("CSV must have at least 5 columns");
        }
        
        Set<String> headerSet = Set.of(headers);
        Set<String> requiredColumns = Set.of("userId", "fullName", "email", "tenantId", "usagePublishedDate");
        
        for (String required : requiredColumns) {
            if (!headerSet.contains(required)) {
                throw new CsvProcessingException("Missing required column: " + required);
            }
        }
    }

    /**
     * Create mapping of header names to column indices
     */
    private Map<String, Integer> createHeaderIndexMap(String[] headers) {
        Map<String, Integer> headerIndexMap = new HashMap<>();
        for (int i = 0; i < headers.length; i++) {
            headerIndexMap.put(headers[i].trim(), i);
        }
        return headerIndexMap;
    }

    /**
     * Process a single CSV row into UserUsageV2 entity
     */
    private UserUsageV2 processRow(String[] row, Map<String, Integer> headerIndexMap, String[] headers) 
            throws CsvProcessingException {
        
        UserUsageV2 userUsage = new UserUsageV2();
        ObjectNode additionalData = objectMapper.createObjectNode();
        
        // Process core columns
        processCoreColumns(userUsage, row, headerIndexMap);
        
        // Process additional columns into JSONB
        processAdditionalColumns(additionalData, row, headerIndexMap, headers);
        
        // Set the additional data as JsonNode
        userUsage.setAdditionalData(additionalData);
        
        return userUsage;
    }

    /**
     * Process core columns that map directly to entity fields
     */
    private void processCoreColumns(UserUsageV2 userUsage, String[] row, Map<String, Integer> headerIndexMap) 
            throws CsvProcessingException {
        
        // Required fields
        userUsage.setUserId(getStringValue(row, headerIndexMap, "userId", true));
        userUsage.setFullName(getStringValue(row, headerIndexMap, "fullName", true));
        userUsage.setEmail(getStringValue(row, headerIndexMap, "email", true));
        userUsage.setTenantId(getStringValue(row, headerIndexMap, "tenantId", true));
        userUsage.setUsagePublishedDate(getDateTimeValue(row, headerIndexMap, "usagePublishedDate", true));
        
        // Optional fields
        userUsage.setTenantName(getStringValue(row, headerIndexMap, "tenantName", false));
        userUsage.setPlanName(getStringValue(row, headerIndexMap, "planName", false));
        userUsage.setPrimaryPhoneNumber(getStringValue(row, headerIndexMap, "primaryPhoneNumber", false));
        userUsage.setAllPhoneNumbers(getStringValue(row, headerIndexMap, "allPhoneNumbers", false));
        userUsage.setStatus(getStringValue(row, headerIndexMap, "status", false));
        userUsage.setCreatedAt(getDateTimeValue(row, headerIndexMap, "createdAt", false));
        userUsage.setUpdatedAt(getDateTimeValue(row, headerIndexMap, "updatedAt", false));
    }

    /**
     * Process additional columns into JSONB data
     */
    private void processAdditionalColumns(ObjectNode additionalData, String[] row, 
                                        Map<String, Integer> headerIndexMap, String[] headers) {
        
        for (String header : headers) {
            String trimmedHeader = header.trim();
            
            // Skip core columns
            if (CORE_COLUMNS.contains(trimmedHeader)) {
                continue;
            }
            
            Integer index = headerIndexMap.get(trimmedHeader);
            if (index != null && index < row.length) {
                String value = row[index].trim();
                
                if (!value.isEmpty() && !value.equalsIgnoreCase("null")) {
                    // Try to parse as different data types
                    addValueToJsonNode(additionalData, trimmedHeader, value);
                }
            }
        }
    }

    /**
     * Add value to JSON node with appropriate data type
     */
    private void addValueToJsonNode(ObjectNode jsonNode, String key, String value) {
        // Try boolean
        if ("true".equalsIgnoreCase(value) || "false".equalsIgnoreCase(value)) {
            jsonNode.put(key, Boolean.parseBoolean(value));
            return;
        }
        
        // Try integer
        try {
            int intValue = Integer.parseInt(value);
            jsonNode.put(key, intValue);
            return;
        } catch (NumberFormatException ignored) {
            // Not an integer
        }
        
        // Try long
        try {
            long longValue = Long.parseLong(value);
            jsonNode.put(key, longValue);
            return;
        } catch (NumberFormatException ignored) {
            // Not a long
        }
        
        // Try double
        try {
            double doubleValue = Double.parseDouble(value);
            jsonNode.put(key, doubleValue);
            return;
        } catch (NumberFormatException ignored) {
            // Not a double
        }
        
        // Default to string
        jsonNode.put(key, value);
    }

    /**
     * Get string value from CSV row
     */
    private String getStringValue(String[] row, Map<String, Integer> headerIndexMap, 
                                 String columnName, boolean required) throws CsvProcessingException {
        Integer index = headerIndexMap.get(columnName);
        
        if (index == null) {
            if (required) {
                throw new CsvProcessingException("Required column not found: " + columnName);
            }
            return null;
        }
        
        if (index >= row.length) {
            if (required) {
                throw new CsvProcessingException("Required column value missing: " + columnName);
            }
            return null;
        }
        
        String value = row[index].trim();
        
        if (value.isEmpty() || value.equalsIgnoreCase("null")) {
            if (required) {
                throw new CsvProcessingException("Required column value is empty: " + columnName);
            }
            return null;
        }
        
        return value;
    }

    /**
     * Get LocalDateTime value from CSV row
     */
    private LocalDateTime getDateTimeValue(String[] row, Map<String, Integer> headerIndexMap, 
                                          String columnName, boolean required) throws CsvProcessingException {
        String stringValue = getStringValue(row, headerIndexMap, columnName, required);
        
        if (stringValue == null) {
            return null;
        }
        
        for (DateTimeFormatter formatter : DATE_FORMATTERS) {
            try {
                return LocalDateTime.parse(stringValue, formatter);
            } catch (DateTimeParseException ignored) {
                // Try next formatter
            }
        }
        
        if (required) {
            throw new CsvProcessingException("Invalid date format for column " + columnName + ": " + stringValue);
        }
        
        logger.warn("Could not parse date value '{}' for column '{}'", stringValue, columnName);
        return null;
    }
}
