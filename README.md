# Customer Success Analytics

A Spring Boot application for daily usage reporting and analytics, similar to Mixpanel functionality. This application provides comprehensive insights into customer onboarding, user engagement, and business metrics.

## Features

- **Dashboard Analytics**: Comprehensive dashboard with key metrics and visualizations
- **Tenant Management**: Track tenant onboarding, industry distribution, and activity metrics
- **User Analytics**: Monitor user engagement, retention, and daily active users (DAU)
- **CSV Data Loading**: Import data from CSV files with robust error handling
- **Interactive Charts**: Beautiful charts and graphs using Chart.js
- **Responsive UI**: Modern, responsive interface built with Bootstrap and Thymeleaf
- **Secure Authentication**: Spring Security with configurable credentials
- **Clean Architecture**: Follows SOLID principles and clean code practices
- **Comprehensive Testing**: Unit tests and integration tests with AssertJ

## Technology Stack

- **Backend**: Spring Boot 3.2.0, Java 17
- **Database**: PostgreSQL (with H2 for testing)
- **Frontend**: <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>trap 5, Chart.js
- **Security**: Spring Security
- **Testing**: JUnit 5, AssertJ, Mockito, Testcontainers
- **Build Tool**: Maven
- **CSV Processing**: OpenCSV

## Prerequisites

- Java 17 or higher
- Maven 3.6 or higher
- PostgreSQL 12 or higher (running on localhost:5432)
- Git

## Database Setup

1. Install PostgreSQL and ensure it's running on localhost:5432
2. Create a database named `customer_success_analytics`:
   ```sql
   CREATE DATABASE customer_success_analytics;
   ```
3. Ensure the following credentials are configured:
   - Username: `postgres`
   - Password: `password`

## Installation and Setup

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd sd-customer-success
   ```

2. **Build the application**:
   ```bash
   mvn clean compile
   ```

3. **Run tests**:
   ```bash
   mvn test
   ```

4. **Start the application**:
   ```bash
   mvn spring-boot:run
   ```

5. **Access the application**:
   - Open your browser and navigate to: http://localhost:8080
   - Login with default credentials:
     - **Email**: <EMAIL>
     - **Password**: test@123

## Loading Initial Data

The application comes with sample CSV files that can be loaded:

1. Navigate to **Data Management** section in the application
2. Click **"Load Initial Data"** to load the provided CSV files:
   - `onboarding-test.csv` - Tenant onboarding data
   - `usage-test.csv` - User usage data
3. Alternatively, upload your own CSV files using the upload forms

## CSV File Format

### Tenant Onboarding CSV
Required columns:
- `tenantId` - Unique tenant identifier
- `tenantName` - Name of the tenant
- `tenantIndustry` - Industry category
- `signedUpAt` - Signup date (ISO format: YYYY-MM-DDTHH:mm:ss.sssZ)
- `tenantUserEmail` - Primary contact email
- Additional metrics columns (see sample file for complete structure)

### User Usage CSV
Required columns:
- `userId` - Unique user identifier
- `fullName` - User's full name
- `email` - User's email address
- `tenantId` - Associated tenant ID
- `usagePublishedDate` - Usage date (ISO format)
- Additional activity columns (see sample file for complete structure)

## Application Structure

```
src/
├── main/
│   ├── java/com/kylas/analytics/
│   │   ├── config/          # Configuration classes
│   │   ├── controller/      # Web controllers
│   │   ├── domain/          # Entities and repositories
│   │   ├── service/         # Business logic services
│   │   └── CustomerSuccessAnalyticsApplication.java
│   └── resources/
│       ├── templates/       # Thymeleaf templates
│       └── application.yml  # Configuration
└── test/                    # Test classes
```

## Key Components

### Services
- **AnalyticsService**: Core business logic for analytics and reporting
- **CsvDataLoadingService**: Handles CSV data import with batch processing
- **CSV Processors**: Specialized processors for different entity types

### Controllers
- **DashboardController**: Main dashboard and analytics views
- **DataManagementController**: CSV upload and data management

### Security
- **SecurityConfig**: Spring Security configuration with form-based authentication

## Features Overview

### Dashboard
- Total tenants, active tenants, total users, daily active users
- User engagement rate and tenant retention rate
- Trend charts for tenant signups and user activity
- Industry and plan distribution charts
- Top performers by activity

### Analytics
- **Tenant Analytics**: Industry distribution, plan analysis, activity metrics
- **User Analytics**: Engagement metrics, retention analysis, DAU tracking
- **Marketplace Apps**: Usage statistics for installed applications
- **Search Functionality**: Search tenants and users by name/email

### Data Management
- Load initial CSV data from workspace
- Upload custom CSV files
- Clear all data functionality
- Data validation and error handling

## Testing

The application includes comprehensive tests:

```bash
# Run all tests
mvn test

# Run specific test class
mvn test -Dtest=AnalyticsServiceTest

# Run tests with coverage
mvn test jacoco:report
```

### Test Categories
- **Unit Tests**: Service layer business logic
- **Integration Tests**: Full application context
- **CSV Processing Tests**: Data import functionality

## Configuration

Key configuration properties in `application.yml`:

```yaml
spring:
  datasource:
    url: ***********************************************************
    username: postgres
    password: password

app:
  csv:
    batch-size: 1000
  analytics:
    cache-duration: 3600
    default-date-range: 30
```

## Security

- **Authentication**: Form-based login with Spring Security
- **Default Credentials**: <EMAIL> / test@123
- **Session Management**: Configurable session timeout
- **CSRF Protection**: Enabled for form submissions

## Performance Features

- **Batch Processing**: CSV data loaded in configurable batches
- **Caching**: Analytics results cached for improved performance
- **Database Optimization**: Proper indexing and query optimization
- **Connection Pooling**: HikariCP for database connections

## Error Handling

- Comprehensive error handling for CSV processing
- User-friendly error messages in the UI
- Detailed logging for debugging
- Graceful handling of malformed data

## Contributing

1. Follow clean code principles and SOLID design patterns
2. Write comprehensive tests for new features
3. Use meaningful commit messages
4. Ensure all tests pass before submitting

## License

This project is licensed under the MIT License.

## Support

For questions or issues, please contact the development team or create an issue in the repository.
